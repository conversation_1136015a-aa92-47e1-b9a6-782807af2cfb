#!/usr/bin/env python3
"""
Run database migration to add heuristic_context column
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'reposense_ai'))

from reposense_ai.database_migration import DatabaseMigration

def run_migration():
    """Run database migration to add heuristic_context column"""
    
    print("🔄 Running database migration to add heuristic_context column...")
    
    # Initialize migration system
    migration = DatabaseMigration("/app/data/reposense.db")  # Use consolidated database
    
    # Run migrations
    success = migration.migrate_database()
    result = {'success': success, 'applied_migrations': [], 'pending_migrations': [], 'error': None}
    
    if result['success']:
        print("✅ Database migration completed successfully!")
        print(f"Applied migrations: {result['applied_migrations']}")
        if result['pending_migrations']:
            print(f"Pending migrations: {result['pending_migrations']}")
    else:
        print("❌ Database migration failed!")
        print(f"Error: {result['error']}")
        if result['applied_migrations']:
            print(f"Successfully applied: {result['applied_migrations']}")
        if result['pending_migrations']:
            print(f"Failed on: {result['pending_migrations']}")
    
    return result['success']

if __name__ == "__main__":
    success = run_migration()
    if success:
        print("\n🎉 Migration complete! You can now create documents with heuristic context.")
    else:
        print("\n❌ Migration failed. Check the error messages above.")
