{% extends "base.html" %}

{% block title %}LDAP User Synchronization - RepoSense AI{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-users-cog"></i> LDAP User Synchronization</h2>
                <div>
                    <button class="btn btn-outline-primary" onclick="testLDAPConnection()">
                        <i class="fas fa-plug"></i> Test Connection
                    </button>
                    <button class="btn btn-primary" onclick="runLDAPSync()">
                        <i class="fas fa-sync"></i> Run Sync Now
                    </button>
                </div>
            </div>

            <!-- LDAP Status Card -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-info-circle"></i> LDAP Sync Status</h5>
                </div>
                <div class="card-body">
                    <div class="row" id="ldap-status-content">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <strong>Status:</strong>
                                <span id="ldap-enabled-status" class="badge badge-secondary">Loading...</span>
                            </div>
                            <div class="mb-3">
                                <strong>LDAP Server:</strong>
                                <span id="ldap-server">-</span>
                            </div>
                            <div class="mb-3">
                                <strong>Sync Interval:</strong>
                                <span id="ldap-sync-interval">-</span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <strong>Last Sync:</strong>
                                <span id="ldap-last-sync">-</span>
                            </div>
                            <div class="mb-3">
                                <strong>LDAP Users:</strong>
                                <span id="ldap-users-count">-</span>
                            </div>
                            <div class="mb-3">
                                <strong>Total Users:</strong>
                                <span id="total-users-count">-</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- LDAP Configuration Card -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-cog"></i> LDAP Configuration</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        LDAP configuration is managed through the main configuration file. 
                        Changes require a service restart to take effect.
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Connection Settings</h6>
                            <ul class="list-unstyled">
                                <li><strong>Server:</strong> {{ config.ldap_server or 'Not configured' }}</li>
                                <li><strong>Port:</strong> {{ config.ldap_port }}</li>
                                <li><strong>Use SSL:</strong> {{ 'Yes' if config.ldap_use_ssl else 'No' }}</li>
                                <li><strong>Base DN:</strong> {{ config.ldap_user_base_dn or 'Not configured' }}</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>Attribute Mapping</h6>
                            <ul class="list-unstyled">
                                <li><strong>Username:</strong> {{ config.ldap_username_attr }}</li>
                                <li><strong>Email:</strong> {{ config.ldap_email_attr }}</li>
                                <li><strong>Full Name:</strong> {{ config.ldap_fullname_attr }}</li>
                                <li><strong>Phone:</strong> {{ config.ldap_phone_attr }}</li>
                                <li><strong>Groups:</strong> {{ config.ldap_groups_attr }}</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Group to Role Mapping Card -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-users"></i> Group to Role Mapping</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>LDAP Group</th>
                                    <th>RepoSense Role</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for group_dn, role in config.ldap_group_role_mapping.items() %}
                                <tr>
                                    <td><code>{{ group_dn }}</code></td>
                                    <td>
                                        <span class="badge badge-{% if role == 'ADMIN' %}danger{% elif role == 'MANAGER' %}warning{% elif role == 'DEVELOPER' %}primary{% else %}secondary{% endif %}">
                                            {{ role }}
                                        </span>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                        <div class="mt-2">
                            <small class="text-muted">
                                <strong>Default Role:</strong> 
                                <span class="badge badge-secondary">{{ config.ldap_default_role }}</span>
                                (assigned to users with no matching groups)
                            </small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sync Log Card -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-list"></i> Recent Sync Activity</h5>
                </div>
                <div class="card-body">
                    <div id="sync-log" class="bg-light p-3" style="height: 300px; overflow-y: auto; font-family: monospace; font-size: 0.9em;">
                        <div class="text-muted">No sync activity yet. Click "Run Sync Now" to start synchronization.</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Loading Modal -->
<div class="modal fade" id="loadingModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-sm modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-body text-center">
                <div class="spinner-border text-primary" role="status">
                    <span class="sr-only">Loading...</span>
                </div>
                <div class="mt-2" id="loading-message">Processing...</div>
            </div>
        </div>
    </div>
</div>

<script>
// LDAP Management JavaScript
let syncInProgress = false;

function updateLDAPStatus() {
    fetch('/api/ldap/status')
        .then(response => response.json())
        .then(data => {
            if (data.enabled) {
                document.getElementById('ldap-enabled-status').textContent = 'Enabled';
                document.getElementById('ldap-enabled-status').className = 'badge badge-success';
                document.getElementById('ldap-server').textContent = data.ldap_server || '-';
                document.getElementById('ldap-sync-interval').textContent = 
                    data.sync_interval ? `${data.sync_interval} seconds` : '-';
                document.getElementById('ldap-last-sync').textContent = 
                    data.last_sync ? new Date(data.last_sync).toLocaleString() : 'Never';
                document.getElementById('ldap-users-count').textContent = data.ldap_users_count || '0';
                document.getElementById('total-users-count').textContent = data.total_users_count || '0';
            } else {
                document.getElementById('ldap-enabled-status').textContent = 'Disabled';
                document.getElementById('ldap-enabled-status').className = 'badge badge-secondary';
                document.getElementById('ldap-server').textContent = 'Not configured';
                document.getElementById('ldap-sync-interval').textContent = '-';
                document.getElementById('ldap-last-sync').textContent = '-';
                document.getElementById('ldap-users-count').textContent = '-';
                document.getElementById('total-users-count').textContent = '-';
            }
        })
        .catch(error => {
            console.error('Error fetching LDAP status:', error);
            document.getElementById('ldap-enabled-status').textContent = 'Error';
            document.getElementById('ldap-enabled-status').className = 'badge badge-danger';
        });
}

function testLDAPConnection() {
    if (syncInProgress) return;
    
    showLoadingModal('Testing LDAP connection...');
    
    fetch('/api/ldap/test-connection', { method: 'POST' })
        .then(response => response.json())
        .then(data => {
            hideLoadingModal();
            if (data.success) {
                showAlert('success', 'LDAP Connection Test', data.message);
                addToSyncLog('SUCCESS', 'Connection test passed: ' + data.message);
            } else {
                showAlert('danger', 'LDAP Connection Test Failed', data.message);
                addToSyncLog('ERROR', 'Connection test failed: ' + data.message);
            }
        })
        .catch(error => {
            hideLoadingModal();
            showAlert('danger', 'Connection Test Error', 'Failed to test LDAP connection: ' + error.message);
            addToSyncLog('ERROR', 'Connection test error: ' + error.message);
        });
}

function runLDAPSync() {
    if (syncInProgress) return;
    
    syncInProgress = true;
    showLoadingModal('Synchronizing users from LDAP...');
    
    fetch('/api/ldap/sync', { method: 'POST' })
        .then(response => response.json())
        .then(data => {
            syncInProgress = false;
            hideLoadingModal();
            
            if (data.success) {
                showAlert('success', 'LDAP Sync Completed', data.message);
                addToSyncLog('SUCCESS', data.message);
                if (data.stats) {
                    addToSyncLog('INFO', `Details: ${JSON.stringify(data.stats)}`);
                }
                updateLDAPStatus(); // Refresh status
            } else {
                showAlert('danger', 'LDAP Sync Failed', data.message);
                addToSyncLog('ERROR', 'Sync failed: ' + data.message);
            }
        })
        .catch(error => {
            syncInProgress = false;
            hideLoadingModal();
            showAlert('danger', 'Sync Error', 'Failed to run LDAP sync: ' + error.message);
            addToSyncLog('ERROR', 'Sync error: ' + error.message);
        });
}

function showLoadingModal(message) {
    document.getElementById('loading-message').textContent = message;
    $('#loadingModal').modal('show');
}

function hideLoadingModal() {
    $('#loadingModal').modal('hide');
}

function showAlert(type, title, message) {
    // Create alert element
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        <strong>${title}:</strong> ${message}
        <button type="button" class="close" data-dismiss="alert">
            <span>&times;</span>
        </button>
    `;
    
    // Insert at top of container
    const container = document.querySelector('.container-fluid');
    container.insertBefore(alertDiv, container.firstChild);
    
    // Auto-dismiss after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

function addToSyncLog(level, message) {
    const logContainer = document.getElementById('sync-log');
    const timestamp = new Date().toLocaleTimeString();
    const logEntry = document.createElement('div');
    
    let levelClass = 'text-muted';
    if (level === 'SUCCESS') levelClass = 'text-success';
    else if (level === 'ERROR') levelClass = 'text-danger';
    else if (level === 'INFO') levelClass = 'text-info';
    
    logEntry.innerHTML = `<span class="text-muted">[${timestamp}]</span> <span class="${levelClass}">${level}:</span> ${message}`;
    
    // Clear placeholder text if present
    if (logContainer.querySelector('.text-muted') && logContainer.children.length === 1) {
        logContainer.innerHTML = '';
    }
    
    logContainer.appendChild(logEntry);
    logContainer.scrollTop = logContainer.scrollHeight;
}

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    updateLDAPStatus();
    
    // Refresh status every 30 seconds
    setInterval(updateLDAPStatus, 30000);
});
</script>
{% endblock %}
