#!/usr/bin/env python3
"""
LDAP Synchronization Service for RepoSense AI
Handles periodic synchronization of users from LDAP directory
"""

import logging
from datetime import datetime
from typing import Dict, List, Optional, Tuple

try:
    import ldap3
    from ldap3 import Server, Connection, ALL, SUBTREE
    LDAP_AVAILABLE = True
except ImportError:
    LDAP_AVAILABLE = False

from models import Config, User, UserRole
from user_database import UserDatabase


class LDAPSyncService:
    """Service for synchronizing users from LDAP directory"""

    def __init__(self, config: Config, user_db: UserDatabase):
        self.config = config
        self.user_db = user_db
        self.logger = logging.getLogger(__name__)
        
        if not LDAP_AVAILABLE:
            self.logger.error("LDAP library (ldap3) not available. Install with: pip install ldap3")
            raise ImportError("ldap3 library required for LDAP sync")
            
        if not config.ldap_sync_enabled:
            self.logger.info("LDAP sync is disabled in configuration")
            return
            
        # Validate required configuration
        self._validate_config()

    def _validate_config(self):
        """Validate LDAP configuration"""
        required_fields = [
            'ldap_server',
            'ldap_bind_dn', 
            'ldap_bind_password',
            'ldap_user_base_dn'
        ]
        
        missing_fields = []
        for field in required_fields:
            if not getattr(self.config, field, None):
                missing_fields.append(field)
                
        if missing_fields:
            raise ValueError(f"Missing required LDAP configuration: {', '.join(missing_fields)}")

    def test_connection(self) -> Tuple[bool, str]:
        """Test LDAP connection and authentication"""
        try:
            server = Server(
                self.config.ldap_server,
                port=self.config.ldap_port,
                use_ssl=self.config.ldap_use_ssl,
                get_info=ALL
            )
            
            conn = Connection(
                server,
                user=self.config.ldap_bind_dn,
                password=self.config.ldap_bind_password,
                auto_bind=True
            )
            
            # Test a simple search
            conn.search(
                search_base=self.config.ldap_user_base_dn,
                search_filter='(objectClass=*)',
                search_scope=SUBTREE,
                size_limit=1
            )
            
            conn.unbind()
            return True, "LDAP connection successful"
            
        except Exception as e:
            self.logger.error(f"LDAP connection test failed: {e}")
            return False, f"LDAP connection failed: {str(e)}"

    def sync_users_from_ldap(self) -> Dict[str, int]:
        """
        Synchronize users from LDAP directory
        
        Returns:
            Dictionary with sync statistics
        """
        stats = {
            "created": 0,
            "updated": 0, 
            "errors": 0,
            "skipped": 0,
            "total_ldap_users": 0
        }
        
        if not self.config.ldap_sync_enabled:
            self.logger.warning("LDAP sync is disabled")
            return stats
            
        try:
            self.logger.info("Starting LDAP user synchronization")
            
            # Fetch users from LDAP
            ldap_users = self._fetch_ldap_users()
            stats["total_ldap_users"] = len(ldap_users)
            
            self.logger.info(f"Found {len(ldap_users)} users in LDAP directory")
            
            for ldap_user in ldap_users:
                try:
                    result = self._sync_single_user(ldap_user)
                    stats[result] += 1
                    
                except Exception as e:
                    self.logger.error(f"Error syncing user {ldap_user.get('username', 'unknown')}: {e}")
                    stats["errors"] += 1
                    
            self.logger.info(f"LDAP sync completed: {stats}")
            return stats
            
        except Exception as e:
            self.logger.error(f"LDAP sync failed: {e}")
            stats["errors"] += 1
            return stats

    def _fetch_ldap_users(self) -> List[Dict]:
        """Fetch users from LDAP directory"""
        users = []
        
        try:
            server = Server(
                self.config.ldap_server,
                port=self.config.ldap_port,
                use_ssl=self.config.ldap_use_ssl,
                get_info=ALL
            )
            
            conn = Connection(
                server,
                user=self.config.ldap_bind_dn,
                password=self.config.ldap_bind_password,
                auto_bind=True
            )
            
            # Search attributes to fetch
            search_attributes = [
                self.config.ldap_username_attr,
                self.config.ldap_email_attr,
                self.config.ldap_fullname_attr,
                self.config.ldap_phone_attr,
                self.config.ldap_groups_attr
            ]
            
            # Perform LDAP search
            conn.search(
                search_base=self.config.ldap_user_base_dn,
                search_filter=self.config.ldap_user_filter,
                search_scope=SUBTREE,
                attributes=search_attributes
            )
            
            for entry in conn.entries:
                try:
                    # Extract user data from LDAP entry
                    user_data = self._extract_user_data(entry)
                    if user_data:
                        users.append(user_data)
                        
                except Exception as e:
                    self.logger.warning(f"Error extracting user data from LDAP entry: {e}")
                    continue
                    
            conn.unbind()
            
        except Exception as e:
            self.logger.error(f"Error fetching users from LDAP: {e}")
            raise
            
        return users

    def _extract_user_data(self, entry) -> Optional[Dict]:
        """Extract user data from LDAP entry"""
        try:
            # Get attribute values (ldap3 returns lists)
            username = self._get_ldap_attr_value(entry, self.config.ldap_username_attr)
            email = self._get_ldap_attr_value(entry, self.config.ldap_email_attr)
            full_name = self._get_ldap_attr_value(entry, self.config.ldap_fullname_attr)
            phone = self._get_ldap_attr_value(entry, self.config.ldap_phone_attr)
            groups = self._get_ldap_attr_values(entry, self.config.ldap_groups_attr)
            
            # Validate required fields
            if not username or not email:
                self.logger.warning(f"Skipping LDAP entry - missing username or email: {entry.entry_dn}")
                return None
                
            return {
                'username': username,
                'email': email,
                'full_name': full_name or username,
                'phone': phone,
                'groups': groups,
                'ldap_dn': str(entry.entry_dn)
            }
            
        except Exception as e:
            self.logger.error(f"Error extracting user data from {entry.entry_dn}: {e}")
            return None

    def _get_ldap_attr_value(self, entry, attr_name: str) -> Optional[str]:
        """Get single attribute value from LDAP entry"""
        if hasattr(entry, attr_name):
            attr_value = getattr(entry, attr_name)
            if attr_value and len(attr_value.values) > 0:
                return str(attr_value.values[0])
        return None

    def _get_ldap_attr_values(self, entry, attr_name: str) -> List[str]:
        """Get multiple attribute values from LDAP entry"""
        if hasattr(entry, attr_name):
            attr_value = getattr(entry, attr_name)
            if attr_value and attr_value.values:
                return [str(val) for val in attr_value.values]
        return []

    def _sync_single_user(self, ldap_user: Dict) -> str:
        """
        Sync a single user from LDAP data
        
        Returns:
            'created', 'updated', or 'skipped'
        """
        username = ldap_user['username']
        
        # Map LDAP groups to RepoSense role
        role = self._map_groups_to_role(ldap_user['groups'])
        
        # Check if user exists locally
        existing_user = self.user_db.get_user_by_username(username)
        
        if existing_user:
            # Update existing user
            if self._update_user_from_ldap(existing_user, ldap_user, role):
                self.logger.info(f"Updated user from LDAP: {username}")
                return "updated"
            else:
                return "skipped"
        else:
            # Create new user
            if self._create_user_from_ldap(ldap_user, role):
                self.logger.info(f"Created user from LDAP: {username}")
                return "created"
            else:
                raise Exception(f"Failed to create user: {username}")

    def _map_groups_to_role(self, user_groups: List[str]) -> UserRole:
        """Map LDAP groups to RepoSense role"""
        # Check each group mapping
        for group_dn, role_name in self.config.ldap_group_role_mapping.items():
            if group_dn in user_groups:
                try:
                    return UserRole(role_name)
                except ValueError:
                    self.logger.warning(f"Invalid role in mapping: {role_name}")
                    continue
        
        # Return default role if no groups match
        try:
            return UserRole(self.config.ldap_default_role)
        except ValueError:
            self.logger.warning(f"Invalid default role: {self.config.ldap_default_role}, using VIEWER")
            return UserRole.VIEWER

    def _update_user_from_ldap(self, existing_user: User, ldap_user: Dict, role: UserRole) -> bool:
        """Update existing user with LDAP data"""
        updated = False
        
        # Update email if changed
        if existing_user.email != ldap_user['email']:
            existing_user.email = ldap_user['email']
            updated = True
            
        # Update full name if changed
        if existing_user.full_name != ldap_user['full_name']:
            existing_user.full_name = ldap_user['full_name']
            updated = True
            
        # Update phone if changed
        new_phone = ldap_user['phone'] or None
        if existing_user.phone != new_phone:
            existing_user.phone = new_phone
            updated = True
            
        # Update role if changed
        if existing_user.role != role:
            existing_user.role = role
            updated = True
            
        if updated:
            existing_user.last_modified = datetime.now().isoformat()
            existing_user.last_ldap_sync = datetime.now().isoformat()
            existing_user.ldap_groups = ldap_user['groups']
            existing_user.ldap_dn = ldap_user['ldap_dn']
            existing_user.ldap_synced = True
            
            return self.user_db.update_user(existing_user)
            
        return False

    def _create_user_from_ldap(self, ldap_user: Dict, role: UserRole) -> bool:
        """Create new user from LDAP data"""
        user = User(
            username=ldap_user['username'],
            email=ldap_user['email'],
            full_name=ldap_user['full_name'],
            phone=ldap_user['phone'] or None,
            role=role,
            ldap_synced=True,
            ldap_dn=ldap_user['ldap_dn'],
            ldap_groups=ldap_user['groups'],
            last_ldap_sync=datetime.now().isoformat(),
            created_date=datetime.now().isoformat(),
            last_modified=datetime.now().isoformat()
        )
        
        return self.user_db.create_user(user)

    def get_sync_status(self) -> Dict:
        """Get LDAP sync status information"""
        if not self.config.ldap_sync_enabled:
            return {"enabled": False, "message": "LDAP sync is disabled"}
            
        # Get users synced from LDAP
        ldap_users = [user for user in self.user_db.get_all_users() if user.ldap_synced]
        
        # Find most recent sync time
        last_sync = None
        if ldap_users:
            sync_times = [user.last_ldap_sync for user in ldap_users if user.last_ldap_sync]
            if sync_times:
                last_sync = max(sync_times)
        
        return {
            "enabled": True,
            "ldap_server": self.config.ldap_server,
            "sync_interval": self.config.ldap_sync_interval,
            "last_sync": last_sync,
            "ldap_users_count": len(ldap_users),
            "total_users_count": len(self.user_db.get_all_users())
        }
