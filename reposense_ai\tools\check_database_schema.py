#!/usr/bin/env python3
"""
Script to check and fix database schema for risk_aggressiveness_used field
"""

import sys
import os
import sqlite3
sys.path.append('/app')

from document_database import DocumentDatabase

def check_and_fix_schema():
    """Check if risk_aggressiveness_used column exists and add it if missing"""
    
    print("🔍 Checking database schema for risk_aggressiveness_used field...")
    
    # Initialize database
    doc_db = DocumentDatabase("/app/data/reposense.db")
    
    try:
        with doc_db._get_connection() as conn:
            # Check existing columns
            cursor = conn.execute("PRAGMA table_info(documents)")
            columns = cursor.fetchall()
            
            print(f"📋 Found {len(columns)} columns in documents table:")
            existing_columns = set()
            for col in columns:
                col_name = col[1]
                col_type = col[2]
                existing_columns.add(col_name)
                print(f"   {col_name} ({col_type})")
            
            # Check if risk_aggressiveness_used exists
            if 'risk_aggressiveness_used' in existing_columns:
                print(f"✅ risk_aggressiveness_used column exists!")
                
                # Check if any documents have this field populated
                cursor = conn.execute("SELECT COUNT(*) FROM documents WHERE risk_aggressiveness_used IS NOT NULL")
                count_with_data = cursor.fetchone()[0]
                
                cursor = conn.execute("SELECT COUNT(*) FROM documents")
                total_count = cursor.fetchone()[0]
                
                print(f"📊 Documents with risk_aggressiveness_used: {count_with_data}/{total_count}")
                
                if count_with_data == 0 and total_count > 0:
                    print("⚠️  Column exists but no documents have risk aggressiveness data")
                    print("   This suggests documents were created before this field was being populated")
                    
            else:
                print(f"❌ risk_aggressiveness_used column is MISSING!")
                print(f"🔧 Adding risk_aggressiveness_used column...")
                
                try:
                    conn.execute("ALTER TABLE documents ADD COLUMN risk_aggressiveness_used TEXT")
                    print(f"✅ Successfully added risk_aggressiveness_used column")
                except sqlite3.OperationalError as e:
                    print(f"❌ Failed to add column: {e}")
                    return False
            
            # Also check for ai_model_used while we're at it
            if 'ai_model_used' not in existing_columns:
                print(f"❌ ai_model_used column is also MISSING!")
                try:
                    conn.execute("ALTER TABLE documents ADD COLUMN ai_model_used TEXT")
                    print(f"✅ Successfully added ai_model_used column")
                except sqlite3.OperationalError as e:
                    print(f"❌ Failed to add ai_model_used column: {e}")
            
            conn.commit()
            
        print(f"\n🎉 Database schema check complete!")
        return True
        
    except Exception as e:
        print(f"❌ Error checking database schema: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    check_and_fix_schema()
