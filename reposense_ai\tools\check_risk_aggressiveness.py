#!/usr/bin/env python3
"""
Script to check risk_aggressiveness_used values in the database
"""

import sys
import os
sys.path.append('/app')

from document_database import DocumentDatabase

def check_risk_aggressiveness():
    """Check risk_aggressiveness_used values for all documents"""
    
    print("🔍 Checking risk aggressiveness values in database...")
    
    # Initialize database
    doc_db = DocumentDatabase("/app/data/reposense.db")
    
    try:
        # Get all documents (using large limit to get all)
        documents = doc_db.get_documents(limit=10000, offset=0)
        print(f"📄 Found {len(documents)} documents in database\n")
        
        for doc in documents:
            print(f"📋 Document ID: {doc.id}")
            print(f"   Repository: {doc.repository_name}")
            print(f"   Revision: {doc.revision}")
            print(f"   Risk Aggressiveness Used: {doc.risk_aggressiveness_used}")
            print(f"   Risk Level: {doc.risk_level}")
            print(f"   Processed Time: {doc.processed_time}")
            print()
        
    except Exception as e:
        print(f"❌ Error checking risk aggressiveness: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_risk_aggressiveness()
