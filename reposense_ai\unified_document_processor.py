#!/usr/bin/env python3
"""
Unified Document Processing Service for RepoSense AI

Consolidates all document processing into a single, coherent pipeline:
- Historical scanning (from repositories)
- File-based processing (from markdown files)
- Real-time processing (from web interface)

This replaces the fragmented approach of having separate DocumentProcessor,
HistoricalScanner processing, and DocumentService processing.
"""

import logging
import re
import threading
import time
from dataclasses import dataclass
from datetime import datetime
from enum import Enum
from pathlib import Path
from queue import Empty, Queue
from typing import Any, Dict, List, Optional, Tuple

from document_database import DocumentDatabase, DocumentRecord
from metadata_extractor import MetadataExtractor
from models import CommitInfo, RepositoryConfig
from notification_system import EventFactory, NotificationSeverity
from ollama_client import OllamaClient


class ProcessingSource(Enum):
    """Source of document processing request"""

    HISTORICAL_SCAN = "historical_scan"
    FILE_SYSTEM = "file_system"
    WEB_INTERFACE = "web_interface"
    API_REQUEST = "api_request"


@dataclass
class ProcessingTask:
    """Unified task for document processing"""

    # Source information
    source: ProcessingSource
    priority: int = 0  # Higher numbers = higher priority

    # Content (one of these will be provided)
    filepath: Optional[str] = None  # For file-based processing
    commit_info: Optional[CommitInfo] = None  # For historical scanning
    content: Optional[str] = None  # For direct content processing

    # Context information
    repository_config: Optional[RepositoryConfig] = None
    document_record: Optional[DocumentRecord] = None

    # Overrides for specific processing (e.g., rescan with different model/aggressiveness)
    override_model: Optional[str] = None
    override_aggressiveness: Optional[str] = None

    def __lt__(self, other):
        return self.priority > other.priority  # Reverse for max-heap behavior


class UnifiedDocumentProcessor:
    """
    Unified document processing service that handles all document processing scenarios:
    1. Historical scanning from repositories
    2. File system scanning of existing documents
    3. Real-time processing from web interface
    4. API-driven processing
    """

    def __init__(
        self,
        output_dir: str = "/app/data/output",
        db_path: str = "/app/data/reposense.db",  # Use consolidated database
        ollama_client: Optional[OllamaClient] = None,
        config_manager=None,
        max_concurrent_tasks: int = 3,
        notification_manager=None,
    ):
        self.output_dir = Path(output_dir)
        self.db = DocumentDatabase(db_path)
        self.ollama_client = ollama_client
        self.config_manager = config_manager
        self.max_concurrent_tasks = max_concurrent_tasks
        self.notification_manager = notification_manager
        self.logger = logging.getLogger(__name__)

        # Unified metadata extractor with specialized models
        self.metadata_extractor = MetadataExtractor(
            ollama_client=ollama_client, config_manager=config_manager
        )

        # Processing queue and control
        self.task_queue: Queue[ProcessingTask] = Queue()
        self.processing_threads: List[threading.Thread] = []
        self.running = False

        # Track which threads are actually busy processing (not just waiting)
        self.busy_threads: set = set()

        # Statistics and tracking
        self.stats = {
            "processed_count": 0,
            "error_count": 0,
            "processing_time_total": 0.0,
            "last_scan_time": 0.0,
        }

        # File change tracking for file system scanning (legacy - now using database)
        self.file_checksums: Dict[str, str] = {}
        # Note: file_mtimes removed - now using database as source of truth for modification times

    def _get_metadata_extraction_timeout(self) -> int:
        """Get appropriate timeout for metadata extraction based on model complexity"""
        try:
            # Get base timeout from config
            base_timeout = 180  # Default 3 minutes
            if self.config_manager:
                config = self.config_manager.load_config()
                base_timeout = getattr(config, "ollama_timeout_base", 180)

            # Check if we're using specialized models that might be slower
            specialized_models = []
            if self.config_manager:
                config = self.config_manager.load_config()
                specialized_models = [
                    getattr(config, "ollama_model_risk_assessment", ""),
                    getattr(config, "ollama_model_code_review", ""),
                    getattr(config, "ollama_model_documentation", ""),
                    getattr(config, "ollama_model", ""),
                ]

            # Check for large models that need extra time
            for model in specialized_models:
                if model and any(
                    size in model.lower() for size in ["20b", "33b", "70b", "180b"]
                ):
                    # Large models need much more time for metadata extraction
                    timeout = max(
                        base_timeout * 2, 360
                    )  # At least 6 minutes for 20B+ models
                    self.logger.info(
                        f"🕐 Using extended timeout {timeout}s for large model: {model}"
                    )
                    return timeout
                elif model and any(
                    size in model.lower() for size in ["7b", "8b", "12b", "13b", "14b"]
                ):
                    # Medium-large models need extra time
                    timeout = int(max(base_timeout * 1.5, 270))  # At least 4.5 minutes
                    self.logger.debug(
                        f"🕐 Using extended timeout {timeout}s for medium-large model: {model}"
                    )
                    return timeout

            # Default timeout for smaller models
            timeout = max(base_timeout, 180)  # At least 3 minutes
            self.logger.debug(
                f"🕐 Using standard timeout {timeout}s for metadata extraction"
            )
            return timeout

        except Exception as e:
            self.logger.warning(f"Error determining metadata extraction timeout: {e}")
            return 360  # Safe fallback: 6 minutes

    def start(self):
        """Start the unified processing service"""
        if self.running:
            return

        self.running = True

        # Start multiple processing threads for concurrent processing
        for i in range(self.max_concurrent_tasks):
            thread = threading.Thread(
                target=self._processing_loop, name=f"UnifiedProcessor-{i}", daemon=True
            )
            thread.start()
            self.processing_threads.append(thread)

        self.logger.info(
            f"Unified document processor started with {self.max_concurrent_tasks} threads"
        )

    def stop(self):
        """Stop the processing service"""
        if not self.running:
            return

        self.running = False

        # Wait for threads to finish
        for thread in self.processing_threads:
            if thread.is_alive():
                thread.join(timeout=5.0)

        self.processing_threads.clear()
        self.busy_threads.clear()  # Clear busy threads tracking
        self.logger.info("Unified document processor stopped")

    def process_commit(
        self,
        commit_info: CommitInfo,
        repository_config: RepositoryConfig,
        documentation: str,
        priority: int = 5,
    ) -> bool:
        """
        Process a commit from historical scanning

        Args:
            commit_info: Commit information from repository
            repository_config: Repository configuration
            documentation: Generated documentation content
            priority: Processing priority (higher = more urgent)

        Returns:
            bool: True if queued successfully
        """
        task = ProcessingTask(
            source=ProcessingSource.HISTORICAL_SCAN,
            priority=priority,
            commit_info=commit_info,
            content=documentation,
            repository_config=repository_config,
        )

        self.task_queue.put(task)
        queue_size = self.task_queue.qsize()
        self.logger.debug(
            f"Queued historical scan task for {commit_info.repository_name} rev {commit_info.revision} (queue size: {queue_size})"
        )
        return True

    def process_commit_with_model(
        self,
        commit_info: CommitInfo,
        repository_config: RepositoryConfig,
        documentation: str,
        priority: int = 5,
        override_model: Optional[str] = None,
        override_aggressiveness: Optional[str] = None,
    ) -> bool:
        """
        Process a commit from historical scanning with specific AI model and aggressiveness overrides

        Args:
            commit_info: Commit information from repository
            repository_config: Repository configuration
            documentation: Generated documentation content
            priority: Processing priority (higher = more urgent)
            override_model: AI model to use for this specific processing
            override_aggressiveness: Risk aggressiveness level to use for this specific processing

        Returns:
            bool: True if queued successfully
        """
        task = ProcessingTask(
            source=ProcessingSource.HISTORICAL_SCAN,
            priority=priority,
            commit_info=commit_info,
            content=documentation,
            repository_config=repository_config,
        )

        # Store the overrides in the task for later use
        if override_model:
            task.override_model = override_model
            self.logger.info(f"Queuing task with model override: {override_model}")

        if override_aggressiveness:
            task.override_aggressiveness = override_aggressiveness
            self.logger.info(
                f"Queuing task with aggressiveness override: {override_aggressiveness}"
            )

        self.task_queue.put(task)
        queue_size = self.task_queue.qsize()
        self.logger.debug(
            f"Queued historical scan task for {commit_info.repository_name} rev {commit_info.revision} with model {override_model} and aggressiveness {override_aggressiveness} (queue size: {queue_size})"
        )
        return True

    def process_file(self, filepath: str, priority: int = 3) -> bool:
        """
        Process a document file from the file system

        Args:
            filepath: Path to the document file
            priority: Processing priority

        Returns:
            bool: True if queued successfully
        """
        task = ProcessingTask(
            source=ProcessingSource.FILE_SYSTEM, priority=priority, filepath=filepath
        )

        self.task_queue.put(task)
        queue_size = self.task_queue.qsize()
        self.logger.debug(
            f"Queued file processing task for {filepath} (queue size: {queue_size})"
        )
        return True

    def process_content(
        self, content: str, document_record: DocumentRecord, priority: int = 8
    ) -> bool:
        """
        Process content directly (for web interface/API)

        Args:
            content: Document content to process
            document_record: Document record with metadata
            priority: Processing priority

        Returns:
            bool: True if queued successfully
        """
        task = ProcessingTask(
            source=ProcessingSource.WEB_INTERFACE,
            priority=priority,
            content=content,
            document_record=document_record,
        )

        self.task_queue.put(task)
        queue_size = self.task_queue.qsize()
        self.logger.debug(
            f"Queued content processing task for {document_record.id} (queue size: {queue_size})"
        )
        return True

    def scan_file_system(self, force_rescan: bool = False) -> int:
        """
        Scan file system for documents to process

        Args:
            force_rescan: Force processing of all files regardless of modification time

        Returns:
            int: Number of files queued for processing
        """
        queued_count = 0

        try:
            repositories_dir = self.output_dir / "repositories"
            if not repositories_dir.exists():
                return 0

            for repo_dir in repositories_dir.iterdir():
                if not repo_dir.is_dir():
                    continue

                docs_dir = repo_dir / "docs"
                if not docs_dir.exists():
                    continue

                for doc_file in docs_dir.glob("*.md"):
                    if not doc_file.is_file():
                        continue

                    try:
                        filepath = str(doc_file)
                        file_mtime = doc_file.stat().st_mtime

                        # Double-check file still exists (avoid race conditions)
                        if not doc_file.exists():
                            continue

                        # Check if file needs processing
                        if self._needs_processing(filepath, file_mtime, force_rescan):
                            # Higher priority for newer files
                            priority = int(file_mtime) % 10  # Keep priority reasonable
                            if self.process_file(filepath, priority):
                                queued_count += 1
                    except (OSError, FileNotFoundError) as e:
                        # File disappeared during scan - skip it
                        self.logger.debug(
                            f"File disappeared during scan: {doc_file} - {e}"
                        )
                        continue

            self.logger.info(
                f"File system scan queued {queued_count} documents for processing"
            )
            return queued_count

        except Exception as e:
            self.logger.error(f"Error during file system scan: {e}")
            return 0

    def get_stats(self) -> Dict[str, Any]:
        """Get processing statistics"""
        # Get queue size safely
        try:
            queue_size = self.task_queue.qsize()
        except Exception as e:
            self.logger.warning(f"Error getting queue size: {e}")
            queue_size = 0

        return {
            **self.stats,
            "queue_size": queue_size,
            "active_threads": len(
                self.busy_threads
            ),  # Only count threads actually processing
            "running": self.running,
        }

    def _get_current_tasks(self) -> list:
        """Get information about currently processing tasks (for user feedback)"""
        current_tasks = []

        # Add basic info about queue status
        try:
            queue_size = self.task_queue.qsize()
        except Exception as e:
            self.logger.warning(f"Error getting queue size in _get_current_tasks: {e}")
            queue_size = 0
        if queue_size > 0:
            if queue_size == 1:
                current_tasks.append(
                    {
                        "type": "queued",
                        "description": "1 document waiting to be processed",
                        "status": "pending",
                        "count": queue_size,
                    }
                )
            else:
                current_tasks.append(
                    {
                        "type": "queued",
                        "description": f"{queue_size} documents waiting to be processed",
                        "status": "pending",
                        "count": queue_size,
                    }
                )

        # Check if threads are actually busy processing (not just waiting)
        active_threads = len(self.busy_threads)
        if active_threads > 0:
            if queue_size > 0:
                # Both queued and processing
                if active_threads == 1:
                    current_tasks.append(
                        {
                            "type": "processing",
                            "description": "1 document currently being processed",
                            "status": "active",
                            "count": active_threads,
                        }
                    )
                else:
                    current_tasks.append(
                        {
                            "type": "processing",
                            "description": f"{active_threads} documents currently being processed",
                            "status": "active",
                            "count": active_threads,
                        }
                    )
            elif active_threads == 1:
                current_tasks.append(
                    {
                        "type": "processing",
                        "description": "Processing document (analyzing with AI models)",
                        "status": "active",
                        "count": active_threads,
                    }
                )
            else:
                current_tasks.append(
                    {
                        "type": "processing",
                        "description": f"Processing {active_threads} documents (analyzing with AI models)",
                        "status": "active",
                        "count": active_threads,
                    }
                )

        return current_tasks

    def _processing_loop(self):
        """Main processing loop for worker threads"""
        thread_name = threading.current_thread().name
        self.logger.debug(f"Processing thread {thread_name} started")

        while self.running:
            try:
                # Get task with timeout
                task = self.task_queue.get(timeout=1.0)
                queue_size_after = self.task_queue.qsize()
                self.logger.debug(
                    f"{thread_name} got task from queue (remaining: {queue_size_after})"
                )

                # Mark this thread as busy
                self.busy_threads.add(thread_name)

                try:
                    # Process the task
                    start_time = time.time()
                    success = self._process_task(task)
                    processing_time = time.time() - start_time

                    # Update statistics
                    if success:
                        self.stats["processed_count"] += 1
                    else:
                        self.stats["error_count"] += 1

                    self.stats["processing_time_total"] += processing_time

                    self.logger.debug(
                        f"{thread_name} processed {task.source.value} task in {processing_time:.3f}s"
                    )
                finally:
                    # Always mark thread as no longer busy
                    self.busy_threads.discard(thread_name)

            except Empty:
                # No tasks available, continue loop (thread is idle)
                continue
            except Exception as e:
                self.logger.error(f"Error in processing thread {thread_name}: {e}")
                self.stats["error_count"] += 1
                # Ensure thread is marked as not busy on error
                self.busy_threads.discard(thread_name)

        self.logger.debug(f"Processing thread {thread_name} stopped")

    def _process_task(self, task: ProcessingTask) -> bool:
        """Process a single task based on its source"""
        try:
            if task.source == ProcessingSource.HISTORICAL_SCAN:
                return self._process_historical_scan_task(task)
            elif task.source == ProcessingSource.FILE_SYSTEM:
                return self._process_file_system_task(task)
            elif task.source in [
                ProcessingSource.WEB_INTERFACE,
                ProcessingSource.API_REQUEST,
            ]:
                return self._process_content_task(task)
            else:
                self.logger.error(f"Unknown task source: {task.source}")
                return False

        except Exception as e:
            self.logger.error(f"Error processing {task.source.value} task: {e}")
            return False

    def _needs_processing(
        self, filepath: str, file_mtime: float, force_rescan: bool
    ) -> bool:
        """Check if a file needs processing using database as source of truth"""
        if force_rescan:
            return True

        try:
            # Check database for existing document with this filepath
            existing_docs = self.db.get_documents_by_filepath(filepath)

            if not existing_docs:
                # No document exists for this filepath - needs processing
                return True

            # Get the most recent document for this filepath
            latest_doc = max(
                existing_docs,
                key=lambda d: d.processed_time if d.processed_time else "",
            )

            # If document was never fully processed, it needs processing
            if not latest_doc.processed_time:
                return True

            # Compare file modification time with database record
            if latest_doc.file_modified_time is None:
                # No file modification time recorded - needs processing
                return True

            # File needs processing if it's been modified since last processing
            return file_mtime > latest_doc.file_modified_time

        except Exception as e:
            self.logger.debug(
                f"Error checking if file needs processing {filepath}: {e}"
            )
            # On error, err on the side of caution and process the file
            return True

    def _process_historical_scan_task(self, task: ProcessingTask) -> bool:
        """Process a historical scan task (from repository commit)"""
        try:
            commit_info = task.commit_info
            repo_config = task.repository_config
            documentation = task.content

            if not commit_info or not repo_config:
                self.logger.error("Historical scan task missing required data")
                return False

            # Documentation can be empty for rescan operations (will be regenerated)
            if documentation is None:
                self.logger.error(
                    "Historical scan task missing documentation parameter"
                )
                return False

            # Get server name from configuration
            server_name = "default"  # Default fallback
            if self.config_manager:
                config = self.config_manager.load_config()
                server_name = (
                    config.server.name
                    if config.server and config.server.name
                    else "default"
                )

            # Generate document ID using centralized logic
            doc_id = self.metadata_extractor.generate_document_id(
                server_name, commit_info.repository_id, int(commit_info.revision)
            )

            # Parse date using centralized logic with fallbacks
            doc_date = self.metadata_extractor.parse_date_with_fallbacks(
                commit_info.date
            )

            # Handle commit message - ALWAYS preserve original message if it exists
            commit_message = commit_info.message
            if not commit_info.message or commit_info.message.strip() == "":
                # Only use AI-generated summary if there's truly no original message
                ai_summary = self.metadata_extractor.extract_ai_summary(documentation)
                if ai_summary:
                    commit_message = f"{ai_summary} (AI-generated summary)"
                    self.logger.debug(
                        f"Using AI-generated summary for revision {commit_info.revision} (no original message)"
                    )
                else:
                    commit_message = "No commit message available"
            else:
                # Log that we're preserving the original message
                self.logger.debug(
                    f'Preserving original commit message for revision {commit_info.revision}: "{commit_message[:50]}..."'
                )
                # Ensure we don't accidentally overwrite with AI summary
                commit_message = commit_info.message

            # Determine repository directory name (prefer repo_config.name which may include branch)
            repo_dir_name = (
                getattr(repo_config, "name", None) or commit_info.repository_name
            )
            file_dir = self.output_dir / "repositories" / repo_dir_name / "docs"
            file_dir.mkdir(parents=True, exist_ok=True)
            filepath = file_dir / f"revision_{commit_info.revision}.md"

            # Write documentation content to disk (overwrite if exists)
            try:
                if filepath.exists():
                    prev_size = filepath.stat().st_size
                    self.logger.info(
                        f"RESCAN_STEP: Overwriting existing documentation file: {filepath} (prev_size={prev_size} bytes)"
                    )
                else:
                    self.logger.info(
                        f"RESCAN_STEP: Writing new documentation file: {filepath}"
                    )

                with open(filepath, "w", encoding="utf-8") as f:
                    f.write(documentation)

                # Verify write
                if not (filepath.exists() and filepath.stat().st_size > 0):
                    self.logger.error(
                        f"RESCAN_STEP: Documentation file {filepath} was not created or is empty"
                    )
                else:
                    self.logger.info(
                        f"RESCAN_STEP: Documentation file written: {filepath} (size={filepath.stat().st_size} bytes)"
                    )
            except Exception as e:
                self.logger.error(
                    f"RESCAN_STEP: Error writing documentation file {filepath}: {e}"
                )

            # Create document record for enhanced prompt context (filepath now matches what UI expects)
            file_stat = filepath.stat() if filepath.exists() else None
            temp_doc_record = DocumentRecord(
                id=doc_id,
                repository_id=commit_info.repository_id,
                repository_name=commit_info.repository_name,
                revision=int(commit_info.revision),
                date=doc_date,
                filename=f"revision_{commit_info.revision}.md",
                filepath=str(filepath),
                size=len(documentation),
                author=commit_info.author,
                commit_message=commit_message,
                changed_paths=commit_info.changed_paths,
                file_modified_time=(file_stat.st_mtime if file_stat else time.time()),
                processed_time=datetime.now(),
                repository_url=repo_config.url,
                repository_type="svn",  # TODO: Make this dynamic based on repo type
            )

            # Extract metadata using centralized extractor with enhanced prompts
            # Add timeout protection to prevent hanging (Windows-compatible)
            try:
                import queue
                import threading

                # Use threading with timeout for Windows compatibility
                result_queue: queue.Queue[Tuple[str, Any]] = queue.Queue()

                def extract_metadata():
                    try:
                        # Check if there are overrides for this task
                        override_model = getattr(task, "override_model", None)
                        override_aggressiveness = getattr(
                            task, "override_aggressiveness", None
                        )

                        if override_model:
                            self.logger.info(
                                f"Using override model for metadata extraction: {override_model}"
                            )
                        if override_aggressiveness:
                            self.logger.info(
                                f"Using override aggressiveness for metadata extraction: {override_aggressiveness}"
                            )

                        result = self.metadata_extractor.extract_all_metadata(
                            documentation,
                            temp_doc_record,
                            override_model,
                            override_aggressiveness,
                        )
                        result_queue.put(("success", result))
                    except Exception as e:
                        # Log the actual error that occurred during metadata extraction
                        self.logger.error(f"Metadata extraction error in thread: {e}")
                        import traceback

                        self.logger.debug(
                            f"Metadata extraction traceback: {traceback.format_exc()}"
                        )
                        result_queue.put(("error", e))

                # Start metadata extraction in separate thread
                extract_thread = threading.Thread(
                    target=extract_metadata, name=f"MetadataExtractor-{doc_id[:8]}"
                )
                extract_thread.daemon = True
                extract_thread.start()

                # Wait for result with timeout - use configurable value based on model complexity
                # For large models like gpt-oss:20b, we need much longer timeouts
                timeout_seconds = self._get_metadata_extraction_timeout()
                try:
                    status, result = result_queue.get(timeout=timeout_seconds)
                    if status == "success":
                        metadata = result
                        self.logger.debug(
                            f"Metadata extraction completed successfully for {doc_id}"
                        )
                    else:
                        self.logger.error(
                            f"Metadata extraction failed with error: {result}"
                        )
                        raise result
                except queue.Empty:
                    self.logger.warning(
                        f"Metadata extraction thread is still running after {timeout_seconds}s timeout"
                    )
                    raise TimeoutError(
                        f"Metadata extraction timed out after {timeout_seconds} seconds"
                    )

            except (TimeoutError, Exception) as e:
                self.logger.warning(f"Metadata extraction failed or timed out: {e}")
                self.logger.info(
                    "Using fallback metadata values to ensure document is processed"
                )

                # Try to preserve AI model information from temp_doc_record if available
                ai_model_used = "fallback-timeout"
                if temp_doc_record and temp_doc_record.ai_model_used:
                    ai_model_used = f"{temp_doc_record.ai_model_used}-timeout"
                    self.logger.info(
                        f"Preserved AI model info from temp record: {ai_model_used}"
                    )
                else:
                    # Try to get the model that would have been used
                    try:
                        from config_manager import ConfigManager

                        config_manager = ConfigManager("data/config.json")
                        config = config_manager.load_config()
                        # Check for specialized model configuration
                        if (
                            hasattr(config, "ollama_model_risk_assessment")
                            and config.ollama_model_risk_assessment
                        ):
                            ai_model_used = (
                                f"{config.ollama_model_risk_assessment}-timeout"
                            )
                            self.logger.info(
                                f"Preserved specialized AI model info from config: {ai_model_used}"
                            )
                        elif hasattr(config, "ollama_model") and config.ollama_model:
                            ai_model_used = f"{config.ollama_model}-timeout"
                            self.logger.info(
                                f"Preserved default AI model info from config: {ai_model_used}"
                            )
                    except Exception as model_error:
                        self.logger.debug(
                            f"Could not determine AI model for fallback: {model_error}"
                        )

                # Provide fallback metadata to ensure document is still processed
                metadata = {
                    "code_review_recommended": True,  # Default to requiring review
                    "code_review_priority": "MEDIUM",
                    "documentation_impact": False,  # Default to no documentation impact
                    "risk_level": "MEDIUM",
                    "ai_model_used": ai_model_used,  # Preserve AI model info when possible
                }

            # Create final document record with metadata
            doc_record = DocumentRecord(
                id=doc_id,
                repository_id=commit_info.repository_id,
                repository_name=commit_info.repository_name,
                revision=int(commit_info.revision),
                date=doc_date,
                filename=f"revision_{commit_info.revision}.md",
                filepath=temp_doc_record.filepath,
                size=len(documentation),
                author=commit_info.author,
                commit_message=commit_message,
                changed_paths=commit_info.changed_paths,
                code_review_recommended=self._convert_to_bool(
                    metadata.get("code_review_recommended")
                ),
                code_review_priority=self._convert_to_string(
                    metadata.get("code_review_priority")
                ),
                documentation_impact=self._convert_to_bool(
                    metadata.get("documentation_impact")
                ),
                risk_level=self._convert_to_string(metadata.get("risk_level")),
                file_modified_time=temp_doc_record.file_modified_time,
                processed_time=datetime.now(),
                ai_model_used=metadata.get("ai_model_used")
                or temp_doc_record.ai_model_used,  # Use metadata first, fallback to temp record
                risk_aggressiveness_used=repo_config.risk_aggressiveness,  # Track aggressiveness level used for analysis
                repository_url=repo_config.url,
                repository_type="svn",
                heuristic_context=temp_doc_record.heuristic_context,  # Copy heuristic context from temp record
            )

            # Check for high-risk commits and emit security alerts
            risk_level = doc_record.risk_level
            if risk_level in ["HIGH", "CRITICAL"]:
                self._emit_security_alert(
                    title=f"High-Risk Commit Detected: {commit_info.repository_name} rev {commit_info.revision}",
                    message=f"Commit {commit_info.revision} in {commit_info.repository_name} has been assessed as {risk_level} risk. "
                    f"Author: {commit_info.author}. Please review immediately.",
                    repository_id=commit_info.repository_id,
                    commit_info={
                        "revision": commit_info.revision,
                        "author": commit_info.author,
                        "message": commit_info.message,
                        "changed_paths": commit_info.changed_paths,
                        "date": commit_info.date,
                    },
                    metadata={
                        "risk_level": risk_level,
                        "ai_model": doc_record.ai_model_used,
                        "aggressiveness": repo_config.risk_aggressiveness,
                        "source": "historical_scan",
                    },
                )

            # Append footer to markdown with AI model and processed time for visibility
            try:
                processed_str = (doc_record.processed_time or datetime.now()).strftime(
                    "%Y-%m-%d %H:%M:%S UTC"
                )
                model_str = (
                    doc_record.ai_model_used if doc_record.ai_model_used else "unknown"
                )
                footer_lines = [
                    "",
                    "---",
                    f"Generated by: {model_str}",
                    f"Processed time: {processed_str}",
                ]
                with open(doc_record.filepath, "a", encoding="utf-8") as f:
                    f.write("\n".join(footer_lines) + "\n")
                self.logger.info(
                    f"RESCAN_STEP: Appended footer to documentation file: {doc_record.filepath}"
                )
            except Exception as e:
                self.logger.error(
                    f"RESCAN_STEP: Failed to append footer to {doc_record.filepath}: {e}"
                )

            # For rescan operations (high priority tasks), delete existing document first to ensure clean update
            if task.priority >= 10:  # High priority indicates rescan operation
                existing_doc = self.db.get_document_by_id(doc_record.id)
                if existing_doc:
                    self.logger.info(
                        f"Rescan operation: deleting existing document {doc_record.id} to ensure complete replacement"
                    )
                    self.db.delete_document(doc_record.id)

            # Store in database
            self.logger.info(
                f"RESCAN_STEP: Upserting document record {doc_record.id} (model_used={doc_record.ai_model_used}, filepath={doc_record.filepath})"
            )
            success = self.db.upsert_document(doc_record)
            if success:
                if task.priority >= 10:
                    self.logger.info(
                        f"✅ Rescan completed: {commit_info.repository_name} rev {commit_info.revision} with fresh SVN data"
                    )
                else:
                    self.logger.info(
                        f"✅ Processed historical scan: {commit_info.repository_name} rev {commit_info.revision}"
                    )

                # Emit processing notification event
                self._emit_processing_event(
                    title=f"Document Processed: {commit_info.repository_name} rev {commit_info.revision}",
                    message=f"Successfully processed revision {commit_info.revision} from {commit_info.repository_name}",
                    repository_id=commit_info.repository_id,
                    success=True,
                    metadata={
                        "source": "historical_scan",
                        "revision": commit_info.revision,
                        "ai_model": doc_record.ai_model_used,
                        "risk_level": doc_record.risk_level,
                    },
                )

                # Create cache invalidation signal for web interface
                self._create_cache_invalidation_signal()
            else:
                self.logger.error(
                    f"❌ Failed to store historical scan: {commit_info.repository_name} rev {commit_info.revision}"
                )

            return success

        except Exception as e:
            self.logger.error(f"Error processing historical scan task: {e}")
            return False

    def _process_file_system_task(self, task: ProcessingTask) -> bool:
        """Process a file system task (existing markdown file)"""
        try:
            filepath = task.filepath
            if not filepath:
                self.logger.error("File system task missing filepath")
                return False

            doc_file = Path(filepath)
            if not doc_file.exists():
                self.logger.debug(
                    f"Document file no longer exists (may have been deleted): {filepath}"
                )
                return True  # Return True since this is not an error condition

            # Read and parse the document file
            doc_record = self._parse_document_file(doc_file)
            if not doc_record:
                self.logger.warning(f"Failed to parse document: {filepath}")
                return False

            # For force rescan, delete existing document first to ensure clean update
            # Check if this is a force rescan by looking for existing document with same ID
            existing_doc = self.db.get_document_by_id(doc_record.id)
            if existing_doc:
                self.logger.debug(
                    f"Force rescan: deleting existing document {doc_record.id} to ensure clean update"
                )
                self.db.delete_document(doc_record.id)

            # Store in database
            success = self.db.upsert_document(doc_record)
            if success:
                self.logger.debug(
                    f"✅ Processed file: {doc_file.name} (AI model: {doc_record.ai_model_used})"
                )

                # Emit processing notification event
                self._emit_processing_event(
                    title=f"File Processed: {doc_file.name}",
                    message=f"Successfully processed file {doc_file.name}",
                    repository_id=doc_record.repository_id,
                    success=True,
                    metadata={
                        "source": "file_system",
                        "filename": doc_file.name,
                        "ai_model": doc_record.ai_model_used,
                        "risk_level": doc_record.risk_level,
                    },
                )

                # Create cache invalidation signal for web interface
                self._create_cache_invalidation_signal()
            else:
                self.logger.error(f"❌ Failed to store file: {doc_file.name}")

                # Emit processing failure event
                self._emit_processing_event(
                    title=f"File Processing Failed: {doc_file.name}",
                    message=f"Failed to store processed file {doc_file.name}",
                    repository_id=doc_record.repository_id
                    if "doc_record" in locals()
                    else None,
                    success=False,
                    metadata={
                        "source": "file_system",
                        "filename": doc_file.name,
                        "error": "Database storage failed",
                    },
                )

            return success

        except FileNotFoundError:
            # Handle file not found specifically (race condition)
            self.logger.debug(
                f"File disappeared during processing (race condition): {task.filepath}"
            )
            return True  # Not an error - file was deleted between scan and processing
        except Exception as e:
            self.logger.error(f"Error processing file system task: {e}")
            return False

    def _process_content_task(self, task: ProcessingTask) -> bool:
        """Process a content task (direct content from web/API)"""
        try:
            content = task.content
            document_record = task.document_record

            if not content or not document_record:
                self.logger.error("Content task missing required data")
                return False

            # Extract metadata using centralized extractor
            metadata = self.metadata_extractor.extract_all_metadata(
                content, document_record
            )

            # Update document record with extracted metadata
            document_record.code_review_recommended = metadata.get(
                "code_review_recommended"
            )
            document_record.code_review_priority = metadata.get("code_review_priority")
            document_record.documentation_impact = metadata.get("documentation_impact")
            document_record.risk_level = metadata.get("risk_level")
            document_record.processed_time = datetime.now()

            # Store in database
            success = self.db.upsert_document(document_record)
            if success:
                self.logger.debug(f"✅ Processed content: {document_record.id}")

                # Emit processing notification event
                self._emit_processing_event(
                    title=f"Content Processed: {document_record.id}",
                    message=f"Successfully processed content for document {document_record.id}",
                    repository_id=document_record.repository_id,
                    success=True,
                    metadata={
                        "source": "web_interface",
                        "document_id": document_record.id,
                        "ai_model": document_record.ai_model_used,
                        "risk_level": document_record.risk_level,
                    },
                )

                # Create cache invalidation signal for web interface
                self._create_cache_invalidation_signal()
            else:
                self.logger.error(f"❌ Failed to store content: {document_record.id}")

                # Emit processing failure event
                self._emit_processing_event(
                    title=f"Content Processing Failed: {document_record.id}",
                    message=f"Failed to store processed content for document {document_record.id}",
                    repository_id=document_record.repository_id,
                    success=False,
                    metadata={
                        "source": "web_interface",
                        "document_id": document_record.id,
                        "error": "Database storage failed",
                    },
                )

            return success

        except Exception as e:
            self.logger.error(f"Error processing content task: {e}")
            return False

    def _parse_document_file(self, doc_file: Path) -> Optional[DocumentRecord]:
        """Parse a document file and create a DocumentRecord"""
        try:
            # Read file content
            content = doc_file.read_text(encoding="utf-8")

            # Extract basic information from filename and path
            filename = doc_file.name

            # Parse repository information from path structure
            # Expected: /app/data/output/repositories/{repo_name}/docs/{filename}
            # Example: /app/data/output/repositories/reposense_cpp_test/docs/revision_1.md
            path_parts = doc_file.parts
            repo_name = "unknown"
            repo_id = "unknown"

            if len(path_parts) >= 4 and "repositories" in path_parts:
                repo_index = path_parts.index("repositories")
                if repo_index + 1 < len(path_parts):
                    base_repo_name = path_parts[repo_index + 1]
                    # Use repository name directly as ID (simpler and more readable)
                    repo_id = base_repo_name
                    repo_name = base_repo_name
                    self.logger.debug(f"Using repository name as ID: {repo_id}")

            # Extract revision from filename (e.g., revision_123.md)
            revision = 1
            revision_match = re.search(r"revision_(\d+)", filename)
            if revision_match:
                revision = int(revision_match.group(1))

            # Initialize document metadata
            author = "Unknown"
            commit_message = "No commit message available"
            changed_paths = None
            doc_date = datetime.now()

            # Try to get metadata from repository first (preferred method)
            parsed_repo_id, parsed_revision = self._parse_document_id_from_filename(
                doc_file.name
            )

            # If repo_id not found in filename, try to extract from path
            if not parsed_repo_id and parsed_revision:
                # Extract from path: /app/data/output/repositories/reposense_cpp_test/docs/revision_12.md
                path_parts = doc_file.parts
                if "repositories" in path_parts:
                    repo_index = path_parts.index("repositories")
                    if repo_index + 1 < len(path_parts):
                        base_repo_name = path_parts[repo_index + 1]
                        # Use repository name directly as ID (simpler and more readable)
                        parsed_repo_id = base_repo_name
                        self.logger.debug(
                            f"Using repository name as ID: {parsed_repo_id}"
                        )

            # Check if document already exists in database to preserve original commit message
            if parsed_repo_id and parsed_revision:
                # Get server name for consistent ID generation
                server_name = "default"  # Default fallback
                if self.config_manager:
                    config = self.config_manager.load_config()
                    server_name = (
                        config.server.name
                        if config.server and config.server.name
                        else "default"
                    )

                doc_id = self.metadata_extractor.generate_document_id(
                    server_name, parsed_repo_id, parsed_revision
                )
                existing_doc = self.db.get_document_by_id(doc_id)

                if (
                    existing_doc
                    and existing_doc.commit_message
                    and not existing_doc.commit_message.endswith(
                        "(AI-generated summary)"
                    )
                ):
                    # Document exists with original commit message - preserve it
                    author = existing_doc.author
                    commit_message = existing_doc.commit_message
                    changed_paths = existing_doc.changed_paths
                    doc_date = existing_doc.date
                    self.logger.debug(
                        f'Preserving existing commit message from database for {doc_id}: "{commit_message[:50]}..."'
                    )
                else:
                    # Try to get fresh data from repository
                    repo_config = self._find_repository_config(parsed_repo_id)
                    if repo_config:
                        backend = self._get_backend_for_repository(repo_config)
                        if backend:
                            commit_info = backend.get_commit_info(
                                repo_config, str(parsed_revision)
                            )
                            if commit_info:
                                # Use repository data (preferred)
                                author = commit_info.author or "Unknown"
                                commit_message = (
                                    commit_info.message or "No commit message available"
                                )
                                changed_paths = commit_info.changed_paths
                                doc_date = (
                                    self.metadata_extractor.parse_date_with_fallbacks(
                                        commit_info.date
                                    )
                                )
                                self.logger.debug(
                                    f"Using fresh repository metadata for {parsed_repo_id} revision {parsed_revision}"
                                )
                            else:
                                self.logger.debug(
                                    f"Could not get commit info from repository for {parsed_repo_id} revision {parsed_revision}"
                                )
                        else:
                            self.logger.debug(
                                f"Could not get backend for repository {parsed_repo_id}"
                            )
                    else:
                        self.logger.debug(
                            f"Repository config not found for {parsed_repo_id}"
                        )

            # FALLBACK ONLY: If repository data unavailable, extract from markdown content
            if commit_message == "No commit message available":
                self.logger.debug(
                    f"Repository unavailable for {doc_file.name}, using markdown content as fallback"
                )
                author = (
                    self.metadata_extractor.extract_field(content, "Author") or author
                )
                commit_message = (
                    self.metadata_extractor.extract_field(content, "Message")
                    or commit_message
                )
                date_field = self.metadata_extractor.extract_field(content, "Date")

                if date_field:
                    try:
                        doc_date = datetime.fromisoformat(
                            date_field.replace("Z", "+00:00")
                        )
                    except ValueError:
                        pass  # Use current time as fallback

                # If no commit message from markdown, try to use AI-generated summary
                if commit_message == "No commit message available":
                    ai_summary = self.metadata_extractor.extract_ai_summary(content)
                    if ai_summary:
                        commit_message = f"{ai_summary} (AI-generated summary)"
                        self.logger.debug(
                            f"Using AI-generated summary as commit message for {doc_file.name} (fallback mode)"
                        )

            # Final fallback for date: use filename date or file modification time
            if doc_date == datetime.now():
                file_stat = doc_file.stat()
                doc_date = datetime.fromtimestamp(file_stat.st_mtime)

            # File metadata
            file_stat = doc_file.stat()

            # Use parsed_repo_id if available (from filename or path lookup), otherwise fall back to repo_id
            final_repo_id = parsed_repo_id if parsed_repo_id else repo_id

            # Get server name from configuration
            server_name = "default"  # Default fallback
            if self.config_manager:
                config = self.config_manager.load_config()
                server_name = (
                    config.server.name
                    if config.server and config.server.name
                    else "default"
                )

            doc_id = self.metadata_extractor.generate_document_id(
                server_name, final_repo_id, revision
            )

            # Create a temporary document record for enhanced prompt context
            temp_doc_record = DocumentRecord(
                id=doc_id,
                repository_id=final_repo_id,  # Use the consistent repository name as ID
                repository_name=repo_name,
                revision=revision,
                date=doc_date,
                filename=filename,
                filepath=str(doc_file),
                size=file_stat.st_size,
                author=author,
                commit_message=commit_message,
                changed_paths=changed_paths,
                file_modified_time=file_stat.st_mtime,
                processed_time=datetime.now(),
            )

            # Extract metadata using centralized extractor with enhanced prompts
            metadata = self.metadata_extractor.extract_all_metadata(
                content, temp_doc_record
            )

            # Use base repository name for display (without branch path)
            display_repo_name = repo_name
            if "/" in repo_name:
                display_repo_name = repo_name.split("/")[0]

            # Get repository metadata for proper diff generation
            repo_config = self._find_repository_config(final_repo_id)
            repository_url = repo_config.url if repo_config else None
            repository_type = repo_config.type if repo_config else "svn"

            # Create final document record with metadata
            return DocumentRecord(
                id=doc_id,
                repository_id=final_repo_id,  # Use the consistent repository name as ID
                repository_name=display_repo_name,  # Use base name for display
                revision=revision,
                date=doc_date,
                filename=filename,
                filepath=str(doc_file),
                size=file_stat.st_size,
                author=author,
                commit_message=commit_message,
                changed_paths=changed_paths,
                code_review_recommended=metadata.get("code_review_recommended"),
                code_review_priority=metadata.get("code_review_priority"),
                documentation_impact=metadata.get("documentation_impact"),
                risk_level=metadata.get("risk_level"),
                file_modified_time=file_stat.st_mtime,
                processed_time=datetime.now(),
                ai_model_used=metadata.get("ai_model_used")
                or temp_doc_record.ai_model_used,  # Use metadata first, fallback to temp record
                heuristic_context=temp_doc_record.heuristic_context,  # Copy heuristic context from temp record
                repository_url=repository_url,  # Add repository URL for diff generation
                repository_type=repository_type,  # Add repository type for diff generation
            )

        except Exception as e:
            self.logger.error(f"Error parsing document file {doc_file}: {e}")
            return None

    def _create_cache_invalidation_signal(self):
        """Create cache invalidation signal for DocumentService"""
        try:
            from pathlib import Path

            cache_signal_file = Path("/app/data/cache_invalidate_signal")
            cache_signal_file.touch()
            self.logger.debug("Created cache invalidation signal for web interface")
        except Exception as e:
            self.logger.warning(f"Could not create cache invalidation signal: {e}")

    def _convert_to_bool(self, value) -> Optional[bool]:
        """Convert various value types to boolean for database storage"""
        if value is None:
            return None
        if isinstance(value, bool):
            return value
        if isinstance(value, str):
            # Handle string representations
            value_lower = value.lower().strip()
            if value_lower in ["true", "yes", "1", "on", "enabled"]:
                return True
            elif value_lower in ["false", "no", "0", "off", "disabled"]:
                return False
            else:
                # For other strings, consider non-empty as True
                return bool(value_lower)
        if isinstance(value, dict):
            # For dictionaries from LLM, check if any values indicate True
            # Look for high-confidence indicators
            high_values = ["high", "yes", "true", "required", "needed", "recommended"]
            for v in value.values():
                if isinstance(v, str) and v.lower() in high_values:
                    return True
            return False
        # For other types, use Python's truthiness
        return bool(value)

    def _convert_to_string(self, value) -> Optional[str]:
        """Convert various value types to string for database storage"""
        if value is None:
            return None
        if isinstance(value, str):
            return value
        if isinstance(value, dict):
            # For dictionaries from LLM, try to extract the most relevant value
            # Look for common keys that indicate priority/level
            priority_keys = [
                "priority",
                "level",
                "severity",
                "risk_level",
                "assessment",
            ]
            for key in priority_keys:
                if key in value:
                    return str(value[key])
            # If no priority keys, look for the highest value
            high_values = ["high", "critical", "urgent"]
            medium_values = ["medium", "moderate", "normal"]
            for v in value.values():
                if isinstance(v, str):
                    v_lower = v.lower()
                    if v_lower in high_values:
                        return "HIGH"
                    elif v_lower in medium_values:
                        return "MEDIUM"
            # Default to first non-empty value
            for v in value.values():
                if v:
                    return str(v)
            return None
        # For other types, convert to string
        return str(value)

    def _parse_document_id_from_filename(
        self, filename: str
    ) -> Tuple[Optional[str], Optional[int]]:
        """Parse repository ID and revision from filename"""
        try:
            # Handle different filename patterns
            # Pattern 1: revision_12.md -> (from path), 12
            # Pattern 2: revision_11_2025-08-14.md -> (from path), 11
            # Pattern 3: reposense_cpp_test_12.md -> reposense_cpp_test, 12

            if filename.startswith("revision_"):
                # Extract revision number
                parts = filename.replace(".md", "").split("_")
                if len(parts) >= 2:
                    try:
                        revision = int(parts[1])
                        return (
                            None,
                            revision,
                        )  # Repository ID will be determined from path
                    except ValueError:
                        pass
            else:
                # Try to parse as repo_name_revision format
                parts = filename.replace(".md", "").split("_")
                if len(parts) >= 2:
                    try:
                        revision = int(parts[-1])
                        repo_id = "_".join(parts[:-1])
                        return repo_id, revision
                    except ValueError:
                        pass

            return None, None
        except Exception as e:
            self.logger.debug(f"Error parsing filename {filename}: {e}")
            return None, None

    def _find_repository_config(
        self, repo_id: Optional[str]
    ) -> Optional["RepositoryConfig"]:
        """Find repository configuration by ID or name"""
        try:
            if not repo_id or not self.config_manager:
                return None

            config = self.config_manager.load_config()

            # Since repositories are now in database, we need to access them differently
            # For now, return None and let the calling code handle the database lookup
            # This method should be updated to use the repository database instead
            self.logger.debug(
                f"Repository lookup by config is deprecated - use repository database instead"
            )
            return None

            # If repo_id contains branch path (e.g., "reposense_cpp_test/trunk"),
            # try to find by base repository name
            if "/" in repo_id:
                base_repo_name = repo_id.split("/")[0]
                self.logger.debug(
                    f"Trying to find repository by base name: {base_repo_name} (from {repo_id})"
                )
                for repo in config.repositories:
                    if repo.name == base_repo_name:
                        self.logger.debug(
                            f"Found repository config for {base_repo_name}"
                        )
                        return repo

            self.logger.debug(f"Repository config not found for {repo_id}")
            return None
        except Exception as e:
            self.logger.debug(f"Error finding repository config for {repo_id}: {e}")
            return None

    def _get_backend_for_repository(
        self, repo_config: "RepositoryConfig"
    ) -> Optional[Any]:
        """Get backend for repository"""
        try:
            from repository_backends import get_backend_manager

            backend_manager = get_backend_manager()
            if not self.config_manager:
                return None
            config = self.config_manager.load_config()
            return backend_manager.get_backend_for_repository(repo_config, config)
        except Exception as e:
            self.logger.debug(
                f"Error getting backend for repository {repo_config.name}: {e}"
            )
            return None

    def _emit_processing_event(
        self,
        title: str,
        message: str,
        repository_id: Optional[str] = None,
        success: bool = True,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> None:
        """Emit a document processing notification event"""
        if not self.notification_manager:
            return

        try:
            event = EventFactory.create_processing_event(
                title=title,
                message=message,
                repository_id=repository_id,
                success=success,
            )

            # Add additional metadata
            if metadata:
                event.metadata.update(metadata)

            self.notification_manager.emit_event(event)

        except Exception as e:
            self.logger.error(f"Error emitting processing event: {e}")

    def _emit_security_alert(
        self,
        title: str,
        message: str,
        repository_id: Optional[str] = None,
        commit_info: Optional[Dict[str, Any]] = None,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> None:
        """Emit a security alert notification event"""
        if not self.notification_manager:
            return

        try:
            event = EventFactory.create_security_alert(
                title=title,
                message=message,
                repository_id=repository_id,
                commit_info=commit_info,
            )

            # Add additional metadata
            if metadata:
                event.metadata.update(metadata)

            self.notification_manager.emit_event(event)

        except Exception as e:
            self.logger.error(f"Error emitting security alert: {e}")
