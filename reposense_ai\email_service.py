#!/usr/bin/env python3
"""
Enhanced Email service for sending notifications
Handles SMTP configuration and email sending functionality for all event types
"""

import fnmatch
import logging
import smtplib
from datetime import datetime
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from typing import Any, Dict, List, Optional

from models import (
    CommitInfo,
    Config,
    NotificationCategory,
    RepositoryConfig,
    RepositoryRelationshipType,
    User,
)
from notification_system import NotificationEvent, NotificationSeverity


class EmailService:
    """Enhanced service for sending email notifications"""

    def __init__(self, config: Config, user_db=None, repo_db=None):
        self.config = config
        self.user_db = user_db
        self.repo_db = repo_db
        self.logger = logging.getLogger(__name__)

        # Email templates for different event types
        self.templates = EmailTemplates()

    def send_notification(self, event: NotificationEvent) -> bool:
        """Send email notification for an event"""
        try:
            # Get recipients for this event
            recipients = self.get_recipients_for_event(event)

            if not recipients:
                self.logger.debug(f"No recipients for event {event.event_id}")
                return True  # Not an error if no recipients

            # Generate email content
            subject, body = self.templates.generate_email_content(event)

            # Send email
            return self._send_email_to_recipients(subject, body, recipients, event)

        except Exception as e:
            self.logger.error(
                f"Error sending notification for event {event.event_id}: {e}"
            )
            return False

    def get_recipients_for_event(self, event: NotificationEvent) -> List[str]:
        """Get email recipients based on event type and user preferences"""
        recipients = set()

        # Global recipients for system-wide events
        if event.category in [
            NotificationCategory.SYSTEM_HEALTH,
            NotificationCategory.MAINTENANCE,
        ]:
            # Check if we have database-based users
            if hasattr(self.config, "users") and self.config.users:
                for user in self.config.users:
                    if (
                        user.enabled
                        and user.role.value == "admin"
                        and self._should_user_receive_event(user, event)
                    ):
                        recipients.add(user.email)
            else:
                # Fallback to global email recipients from config
                self.logger.debug("Using global email recipients for system-wide event")
                recipients.update(self.config.email_recipients)

        # Repository-specific recipients
        if event.repository_id:
            if self.repo_db and self.user_db:
                # Use database-based repository-user relationships
                repo = self.repo_db.get_repository_by_id(event.repository_id)
                if repo and repo.user_relationships:
                    self.logger.debug(
                        f"Using database-based recipients for repository event: {event.repository_id}"
                    )
                    for relationship in repo.user_relationships:
                        user = self.user_db.get_user_by_id(relationship.user_id)
                        if user and user.enabled:
                            # Check if user should receive this specific event
                            if self._should_user_receive_repository_event(
                                user, relationship, event
                            ):
                                recipients.add(user.email)
                                self.logger.debug(
                                    f"Added repository recipient: {user.email}"
                                )
                else:
                    self.logger.debug(
                        f"No user relationships found for repository: {event.repository_id}"
                    )
            else:
                # Fallback to global recipients when database not available
                self.logger.debug(
                    f"Using global email recipients for repository event: {event.repository_id} (no database)"
                )
                recipients.update(self.config.email_recipients)

        # Add global email recipients for all events as fallback
        recipients.update(self.config.email_recipients)

        return list(recipients)

    def _should_user_receive_event(self, user: User, event: NotificationEvent) -> bool:
        """Check if user should receive this event based on global preferences"""
        # Check if user has this category enabled
        if event.category not in user.notification_preferences.enabled_categories:
            return False

        # Check severity level
        severity_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        user_min_level = user.notification_preferences.min_severity
        if severity_levels.index(event.severity.value) < severity_levels.index(
            user_min_level
        ):
            return False

        return True

    def _should_user_receive_event_for_repository(
        self, user: User, relationship, event: NotificationEvent
    ) -> bool:
        """Check if user should receive this event based on repository relationship"""
        prefs = relationship.notification_preferences

        # Check category preferences
        if event.category not in prefs.enabled_categories:
            return False

        # Special handling for commits
        if event.category == NotificationCategory.COMMITS:
            # Check if it's the user's own commit
            if event.user_id == user.username and not prefs.notify_on_own_commits:
                return False

            # Check risk level filtering
            if prefs.notify_on_high_risk_only and event.metadata.get(
                "risk_level", "LOW"
            ) not in ["HIGH", "CRITICAL"]:
                return False

            # Check path filtering
            if prefs.notify_on_specific_paths and event.metadata.get("changed_paths"):
                changed_paths = event.metadata.get("changed_paths", [])
                if not any(
                    self._path_matches_pattern(path, pattern)
                    for path in changed_paths
                    for pattern in prefs.notify_on_specific_paths
                ):
                    return False

        # Check global severity filtering
        return self._should_user_receive_event(user, event)

    def _path_matches_pattern(self, path: str, pattern: str) -> bool:
        """Check if a file path matches a pattern using fnmatch"""
        return fnmatch.fnmatch(path, pattern)

    def _should_user_receive_repository_event(
        self, user, relationship, event: NotificationEvent
    ) -> bool:
        """Check if a user should receive a repository-specific event based on their relationship preferences"""
        try:
            prefs = relationship.notification_preferences

            # Check if the event category is enabled for this repository relationship
            if event.category not in prefs.enabled_categories:
                return False

            # Special handling for commits
            if event.category == NotificationCategory.COMMITS:
                # Check if it's the user's own commit
                if event.user_id == user.username and not prefs.notify_on_own_commits:
                    return False

                # Check risk level filtering
                if prefs.notify_on_high_risk_only and event.metadata.get(
                    "risk_level", "LOW"
                ) not in ["HIGH", "CRITICAL"]:
                    return False

                # Check path filtering
                if prefs.notify_on_specific_paths and event.metadata.get(
                    "changed_paths"
                ):
                    changed_paths = event.metadata.get("changed_paths", [])
                    if not any(
                        self._path_matches_pattern(path, pattern)
                        for path in changed_paths
                        for pattern in prefs.notify_on_specific_paths
                    ):
                        return False

            # Check delivery preferences (immediate vs digest)
            # For now, only handle immediate notifications
            if not prefs.immediate_notification:
                # TODO: Implement digest functionality
                return False

            return True

        except Exception as e:
            self.logger.error(
                f"Error checking repository event preferences for user {user.username}: {e}"
            )
            # Default to allowing the notification on error
            return True

    def _send_email_to_recipients(
        self, subject: str, body: str, recipients: List[str], event: NotificationEvent
    ) -> bool:
        """Send email to a list of recipients"""
        try:
            msg = MIMEMultipart()
            msg["From"] = self.config.email_from
            msg["To"] = ", ".join(recipients)
            msg["Subject"] = subject

            msg.attach(MIMEText(body, "plain"))

            # Connect to SMTP server
            with smtplib.SMTP(self.config.smtp_host, self.config.smtp_port) as server:
                if self.config.smtp_username and self.config.smtp_password:
                    server.starttls()
                    server.login(self.config.smtp_username, self.config.smtp_password)

                server.send_message(msg)

            self.logger.info(
                f"Email sent for event {event.event_id} to {len(recipients)} recipients"
            )
            return True

        except Exception as e:
            self.logger.error(f"Error sending email: {e}")
            return False

    def send_email(self, subject: str, body: str, commit: CommitInfo):
        """Send email notification"""
        # Use global recipients for now
        # TODO: Implement database-based repository-specific recipients
        recipients = self.config.email_recipients

        if not recipients:
            self.logger.warning(
                f"No email recipients configured for repository {commit.repository_name} (ID: {commit.repository_id})"
            )
            return

        try:
            msg = MIMEMultipart()
            msg["From"] = self.config.email_from
            msg["To"] = ", ".join(recipients)
            msg["Subject"] = subject

            # Add commit details to email body
            full_body = f"""
{body}

---
Commit Details:
- Revision: {commit.revision}
- Author: {commit.author}
- Date: {commit.date}
- Message: {commit.message}

Changed Files:
{chr(10).join("- " + path for path in commit.changed_paths)}
"""

            msg.attach(MIMEText(full_body, "plain"))

            # Connect to SMTP server
            with smtplib.SMTP(self.config.smtp_host, self.config.smtp_port) as server:
                if self.config.smtp_username and self.config.smtp_password:
                    server.starttls()
                    server.login(self.config.smtp_username, self.config.smtp_password)

                server.send_message(msg)

            self.logger.info(f"Email sent for revision {commit.revision}")
            return True

        except Exception as e:
            self.logger.error(f"Error sending email: {e}")
            return False

    def is_configured(self) -> bool:
        """Check if email service is properly configured"""
        return bool(
            self.config.email_recipients
            and self.config.smtp_host
            and self.config.email_from
        )


class EmailTemplates:
    """Email templates for different notification types"""

    def generate_email_content(self, event: NotificationEvent) -> tuple[str, str]:
        """Generate email subject and body for an event"""
        if event.category == NotificationCategory.COMMITS:
            return self._generate_commit_email(event)
        elif event.category == NotificationCategory.SYSTEM_HEALTH:
            return self._generate_system_health_email(event)
        elif event.category == NotificationCategory.SECURITY_ALERTS:
            return self._generate_security_alert_email(event)
        elif event.category == NotificationCategory.PROCESSING_STATUS:
            return self._generate_processing_email(event)
        elif event.category == NotificationCategory.REPOSITORY_MGMT:
            return self._generate_repository_email(event)
        elif event.category == NotificationCategory.MAINTENANCE:
            return self._generate_maintenance_email(event)
        else:
            return self._generate_generic_email(event)

    def _generate_commit_email(self, event: NotificationEvent) -> tuple[str, str]:
        """Generate email for commit events"""
        risk_level = event.metadata.get("risk_level", "LOW")
        risk_indicator = (
            "🔴"
            if risk_level in ["HIGH", "CRITICAL"]
            else "🟡"
            if risk_level == "MEDIUM"
            else "🟢"
        )

        subject = f"{risk_indicator} {event.title}"

        body = f"""
{event.message}

Risk Level: {risk_level}
Repository: {event.repository_id}
Timestamp: {event.timestamp.strftime("%Y-%m-%d %H:%M:%S")}

"""

        if event.commit_info:
            body += f"""
Commit Details:
- Revision: {event.commit_info.get("revision", "unknown")}
- Author: {event.commit_info.get("author", "unknown")}
- Date: {event.commit_info.get("date", "unknown")}
- Message: {event.commit_info.get("message", "No message")}

Changed Files:
{chr(10).join("- " + path for path in event.metadata.get("changed_paths", []))}
"""

        return subject, body

    def _generate_system_health_email(
        self, event: NotificationEvent
    ) -> tuple[str, str]:
        """Generate email for system health events"""
        severity_icon = "🚨" if event.severity == NotificationSeverity.CRITICAL else "⚠️"

        subject = f"{severity_icon} RepoSense AI: {event.title}"

        body = f"""
{event.message}

Severity: {event.severity.value}
Component: {event.metadata.get("component", "system")}
Timestamp: {event.timestamp.strftime("%Y-%m-%d %H:%M:%S")}

Please check the system status and take appropriate action if needed.
"""

        return subject, body

    def _generate_security_alert_email(
        self, event: NotificationEvent
    ) -> tuple[str, str]:
        """Generate email for security alerts"""
        subject = f"🔒 SECURITY ALERT: {event.title}"

        body = f"""
SECURITY ALERT

{event.message}

Repository: {event.repository_id or "System-wide"}
Timestamp: {event.timestamp.strftime("%Y-%m-%d %H:%M:%S")}

This alert requires immediate attention. Please review the changes and take appropriate security measures.
"""

        if event.commit_info:
            body += f"""

Related Commit:
- Revision: {event.commit_info.get("revision", "unknown")}
- Author: {event.commit_info.get("author", "unknown")}
"""

        return subject, body

    def _generate_processing_email(self, event: NotificationEvent) -> tuple[str, str]:
        """Generate email for processing events"""
        success = event.metadata.get("processing_success", True)
        icon = "✅" if success else "❌"

        subject = f"{icon} {event.title}"

        body = f"""
{event.message}

Repository: {event.repository_id or "System-wide"}
Status: {"Success" if success else "Failed"}
Timestamp: {event.timestamp.strftime("%Y-%m-%d %H:%M:%S")}
"""

        return subject, body

    def _generate_repository_email(self, event: NotificationEvent) -> tuple[str, str]:
        """Generate email for repository management events"""
        subject = f"📁 Repository Update: {event.title}"

        body = f"""
{event.message}

Repository: {event.repository_id}
Timestamp: {event.timestamp.strftime("%Y-%m-%d %H:%M:%S")}
"""

        return subject, body

    def _generate_maintenance_email(self, event: NotificationEvent) -> tuple[str, str]:
        """Generate email for maintenance events"""
        subject = f"🔧 Maintenance: {event.title}"

        body = f"""
{event.message}

Severity: {event.severity.value}
Timestamp: {event.timestamp.strftime("%Y-%m-%d %H:%M:%S")}

Please review system resources and take action if necessary.
"""

        return subject, body

    def _generate_generic_email(self, event: NotificationEvent) -> tuple[str, str]:
        """Generate generic email for unknown event types"""
        subject = f"RepoSense AI: {event.title}"

        body = f"""
{event.message}

Category: {event.category.value}
Severity: {event.severity.value}
Timestamp: {event.timestamp.strftime("%Y-%m-%d %H:%M:%S")}
"""

        return subject, body
