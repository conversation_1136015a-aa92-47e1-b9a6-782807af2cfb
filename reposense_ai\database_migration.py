"""
Database Migration System for RepoSense AI

Handles database schema creation, migrations, and version management.
Ensures smooth upgrades and initialization for new installations.
"""

import logging
import os
import sqlite3
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional


class DatabaseMigration:
    """Database migration and schema management system"""

    def __init__(self, db_path: str):
        self.db_path = db_path
        self.logger = logging.getLogger(__name__)

        # Migration definitions
        self.migrations = {
            1: self._migration_001_initial_schema,
            2: self._migration_002_add_indexes,
            3: self._migration_003_add_metadata_fields,
            4: self._migration_004_add_diff_field,
            5: self._migration_005_add_timestamps,
            6: self._migration_006_replace_diff_with_metadata,
            7: self._migration_007_add_changed_paths,
            8: self._migration_008_add_ai_summary,
            9: self._migration_009_add_ai_model_used,
            10: self._migration_010_add_heuristic_context,
            11: self._migration_011_create_users_table,
            12: self._migration_012_create_repositories_table,
            13: self._migration_013_create_notifications_tables,
        }

    def initialize_database(self) -> bool:
        """Initialize database with latest schema"""
        try:
            # Ensure directory exists
            db_dir = Path(self.db_path).parent
            db_dir.mkdir(parents=True, exist_ok=True)

            # Check if database exists
            db_exists = Path(self.db_path).exists()

            if not db_exists:
                self.logger.info("Creating new database with latest schema")
                return self._create_fresh_database()
            else:
                self.logger.info("Database exists, checking for migrations")
                return self.migrate_database()

        except Exception as e:
            self.logger.error(f"Error initializing database: {e}")
            return False

    def migrate_database(self) -> bool:
        """Apply pending migrations to existing database"""
        try:
            current_version = self._get_current_version()
            latest_version = max(self.migrations.keys())

            if current_version >= latest_version:
                self.logger.info(f"Database is up to date (version {current_version})")
                return True

            self.logger.info(
                f"Migrating database from version {current_version} to {latest_version}"
            )

            # Apply migrations in order
            for version in range(current_version + 1, latest_version + 1):
                if version in self.migrations:
                    self.logger.info(f"Applying migration {version}")
                    if not self._apply_migration(version):
                        self.logger.error(f"Migration {version} failed")
                        return False
                    self._update_version(version)

            self.logger.info("Database migration completed successfully")
            return True

        except Exception as e:
            self.logger.error(f"Error during database migration: {e}")
            return False

    def _create_fresh_database(self) -> bool:
        """Create a fresh database with the latest schema"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                # Create migration tracking table first
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS schema_migrations (
                        version INTEGER PRIMARY KEY,
                        applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        description TEXT
                    )
                """)

                # Apply all migrations in order
                for version in sorted(self.migrations.keys()):
                    migration_func = self.migrations[version]
                    migration_func(conn)

                    # Record migration
                    conn.execute(
                        "INSERT INTO schema_migrations (version, description) VALUES (?, ?)",
                        (version, migration_func.__name__),
                    )

                conn.commit()
                self.logger.info(
                    f"Fresh database created with version {max(self.migrations.keys())}"
                )
                return True

        except Exception as e:
            self.logger.error(f"Error creating fresh database: {e}")
            return False

    def _get_current_version(self) -> int:
        """Get current database schema version"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                # Check if migration table exists
                cursor = conn.execute("""
                    SELECT name FROM sqlite_master 
                    WHERE type='table' AND name='schema_migrations'
                """)

                if not cursor.fetchone():
                    return 0  # No migration table = version 0

                # Get latest version
                cursor = conn.execute("SELECT MAX(version) FROM schema_migrations")
                result = cursor.fetchone()
                return result[0] if result[0] is not None else 0

        except Exception as e:
            self.logger.error(f"Error getting current version: {e}")
            return 0

    def _apply_migration(self, version: int) -> bool:
        """Apply a specific migration"""
        try:
            migration_func = self.migrations[version]

            with sqlite3.connect(self.db_path) as conn:
                migration_func(conn)
                conn.commit()
                return True

        except Exception as e:
            self.logger.error(f"Error applying migration {version}: {e}")
            return False

    def _update_version(self, version: int):
        """Update migration version in database"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                migration_func = self.migrations[version]
                conn.execute(
                    "INSERT INTO schema_migrations (version, description) VALUES (?, ?)",
                    (version, migration_func.__name__),
                )
                conn.commit()
        except Exception as e:
            self.logger.error(f"Error updating version to {version}: {e}")

    # Migration definitions

    def _migration_001_initial_schema(self, conn: sqlite3.Connection):
        """Migration 001: Create initial documents table"""
        conn.execute("""
            CREATE TABLE IF NOT EXISTS documents (
                id TEXT PRIMARY KEY,
                repository_id TEXT NOT NULL,
                repository_name TEXT NOT NULL,
                revision INTEGER NOT NULL,
                date TIMESTAMP NOT NULL,
                filename TEXT NOT NULL,
                filepath TEXT NOT NULL,
                size INTEGER NOT NULL,
                author TEXT NOT NULL,
                commit_message TEXT NOT NULL,
                file_modified_time REAL,
                processed_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)

    def _migration_002_add_indexes(self, conn: sqlite3.Connection):
        """Migration 002: Add performance indexes"""
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_documents_repository_id ON documents(repository_id)",
            "CREATE INDEX IF NOT EXISTS idx_documents_revision ON documents(revision)",
            "CREATE INDEX IF NOT EXISTS idx_documents_date ON documents(date DESC)",
            "CREATE INDEX IF NOT EXISTS idx_documents_processed_time ON documents(processed_time DESC)",
            "CREATE INDEX IF NOT EXISTS idx_documents_repo_revision ON documents(repository_id, revision)",
        ]

        for index_sql in indexes:
            conn.execute(index_sql)

    def _migration_003_add_metadata_fields(self, conn: sqlite3.Connection):
        """Migration 003: Add LLM analysis metadata fields"""
        metadata_fields = [
            "ALTER TABLE documents ADD COLUMN code_review_recommended INTEGER",
            "ALTER TABLE documents ADD COLUMN code_review_priority TEXT",
            "ALTER TABLE documents ADD COLUMN documentation_impact INTEGER",
            "ALTER TABLE documents ADD COLUMN risk_level TEXT",
        ]

        for field_sql in metadata_fields:
            try:
                conn.execute(field_sql)
            except sqlite3.OperationalError as e:
                if "duplicate column name" not in str(e).lower():
                    raise  # Re-raise if it's not a duplicate column error

        # Add indexes for new fields
        new_indexes = [
            "CREATE INDEX IF NOT EXISTS idx_documents_code_review ON documents(code_review_recommended)",
            "CREATE INDEX IF NOT EXISTS idx_documents_doc_impact ON documents(documentation_impact)",
            "CREATE INDEX IF NOT EXISTS idx_documents_risk_level ON documents(risk_level)",
        ]

        for index_sql in new_indexes:
            conn.execute(index_sql)

    def _migration_004_add_diff_field(self, conn: sqlite3.Connection):
        """Migration 004: Add diff field for storing SCM diff content"""
        try:
            conn.execute("ALTER TABLE documents ADD COLUMN diff TEXT")
        except sqlite3.OperationalError as e:
            if "duplicate column name" not in str(e).lower():
                raise  # Re-raise if it's not a duplicate column error

    def _migration_005_add_timestamps(self, conn: sqlite3.Connection):
        """Migration 005: Add created_at and updated_at timestamp fields"""
        timestamp_fields = [
            "ALTER TABLE documents ADD COLUMN created_at TEXT DEFAULT CURRENT_TIMESTAMP",
            "ALTER TABLE documents ADD COLUMN updated_at TEXT DEFAULT CURRENT_TIMESTAMP",
        ]

        for field_sql in timestamp_fields:
            try:
                conn.execute(field_sql)
            except sqlite3.OperationalError as e:
                if "duplicate column name" not in str(e).lower():
                    raise  # Re-raise if it's not a duplicate column error

    def _migration_006_replace_diff_with_metadata(self, conn: sqlite3.Connection):
        """Migration 006: Replace diff column with repository metadata for on-demand diff generation"""
        metadata_fields = [
            "ALTER TABLE documents ADD COLUMN repository_url TEXT",
            "ALTER TABLE documents ADD COLUMN repository_type TEXT",
        ]

        for field_sql in metadata_fields:
            try:
                conn.execute(field_sql)
            except sqlite3.OperationalError as e:
                if "duplicate column name" not in str(e).lower():
                    raise  # Re-raise if it's not a duplicate column error

        # Note: We keep the diff column for backward compatibility but won't use it going forward
        # In a future migration, we could drop it after ensuring all systems are updated

    def _migration_007_add_changed_paths(self, conn: sqlite3.Connection):
        """Migration 007: Add changed_paths field to store list of files changed in commit"""
        conn.execute("""
            ALTER TABLE documents
            ADD COLUMN changed_paths TEXT
        """)
        self.logger.info("Added changed_paths column to documents table")

    def get_migration_status(self) -> Dict[str, Any]:
        """Get current migration status and information"""
        try:
            current_version = self._get_current_version()
            latest_version = max(self.migrations.keys())

            # Get applied migrations
            applied_migrations = []
            try:
                with sqlite3.connect(self.db_path) as conn:
                    cursor = conn.execute("""
                        SELECT version, applied_at, description 
                        FROM schema_migrations 
                        ORDER BY version
                    """)
                    applied_migrations = [
                        {"version": row[0], "applied_at": row[1], "description": row[2]}
                        for row in cursor.fetchall()
                    ]
            except Exception:
                pass  # Migration table might not exist

            return {
                "current_version": current_version,
                "latest_version": latest_version,
                "needs_migration": current_version < latest_version,
                "applied_migrations": applied_migrations,
                "pending_migrations": [
                    v for v in self.migrations.keys() if v > current_version
                ],
            }

        except Exception as e:
            self.logger.error(f"Error getting migration status: {e}")
            return {
                "current_version": 0,
                "latest_version": max(self.migrations.keys()),
                "needs_migration": True,
                "applied_migrations": [],
                "pending_migrations": list(self.migrations.keys()),
                "error": str(e),
            }

    def _migration_008_add_ai_summary(self, conn: sqlite3.Connection):
        """Migration 008: Add AI summary field"""
        self.logger.info("Adding AI summary field to documents table")

        # Add the summary column
        conn.execute("""
            ALTER TABLE documents
            ADD COLUMN ai_summary TEXT
        """)

        # Add index for summary field (for future search capabilities)
        conn.execute("""
            CREATE INDEX IF NOT EXISTS idx_ai_summary
            ON documents(ai_summary)
        """)

        self.logger.info("AI summary field added successfully")

    def _migration_009_add_ai_model_used(self, conn: sqlite3.Connection):
        """Migration 9: Add ai_model_used field to track which AI model was used for analysis"""
        self.logger.info("Running migration 9: Adding ai_model_used field")

        # Add ai_model_used column
        conn.execute("""
            ALTER TABLE documents
            ADD COLUMN ai_model_used TEXT
        """)

        # Add index for ai_model_used field (for analytics and filtering)
        conn.execute("""
            CREATE INDEX IF NOT EXISTS idx_ai_model_used
            ON documents(ai_model_used)
        """)

        self.logger.info("AI model used field added successfully")

    def _migration_010_add_heuristic_context(self, conn: sqlite3.Connection):
        """Migration 10: Add heuristic_context field to store confidence ratings and analysis indicators"""
        self.logger.info("Running migration 10: Adding heuristic_context field")

        # Add heuristic_context column
        conn.execute("""
            ALTER TABLE documents
            ADD COLUMN heuristic_context TEXT
        """)

        self.logger.info("Heuristic context field added successfully")

    def _migration_011_create_users_table(self, conn: sqlite3.Connection):
        """Migration 011: Create users table to move users from config.json to database"""
        self.logger.info("Running migration 11: Creating users table")

        # Create users table
        conn.execute("""
            CREATE TABLE IF NOT EXISTS users (
                id TEXT PRIMARY KEY,
                username TEXT NOT NULL UNIQUE,
                email TEXT NOT NULL UNIQUE,
                full_name TEXT NOT NULL,
                role TEXT NOT NULL,
                enabled INTEGER NOT NULL DEFAULT 1,
                receive_all_notifications INTEGER NOT NULL DEFAULT 0,
                repository_subscriptions TEXT,  -- JSON array of repository IDs
                phone TEXT,
                department TEXT,
                created_date TEXT,
                last_modified TEXT,
                notification_preferences TEXT  -- JSON object for enhanced notification preferences
            )
        """)

        # Create indexes for performance
        conn.execute("CREATE INDEX IF NOT EXISTS idx_users_username ON users(username)")
        conn.execute("CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)")
        conn.execute("CREATE INDEX IF NOT EXISTS idx_users_role ON users(role)")
        conn.execute("CREATE INDEX IF NOT EXISTS idx_users_enabled ON users(enabled)")

        self.logger.info("Users table created successfully")

    def _migration_012_create_repositories_table(self, conn: sqlite3.Connection):
        """Migration 012: Create repositories table to move repositories from config.json to database"""
        self.logger.info("Running migration 12: Creating repositories table")

        # Create repositories table
        conn.execute("""
            CREATE TABLE IF NOT EXISTS repositories (
                id TEXT PRIMARY KEY,
                name TEXT NOT NULL UNIQUE,
                url TEXT NOT NULL,
                type TEXT NOT NULL DEFAULT 'svn',
                username TEXT,
                password TEXT,
                last_revision INTEGER DEFAULT 0,
                last_commit_date TEXT,
                last_processed_time TEXT,
                enabled INTEGER NOT NULL DEFAULT 1,
                branch_path TEXT,
                monitor_all_branches INTEGER DEFAULT 0,
                assigned_users TEXT,  -- JSON array of user IDs
                email_recipients TEXT,  -- JSON array of email addresses
                risk_aggressiveness TEXT DEFAULT 'BALANCED',
                risk_description TEXT,
                historical_scan TEXT,  -- JSON object for historical scan config
                user_relationships TEXT,  -- JSON array of user relationships
                path_watchers TEXT,  -- JSON object for path watchers
                created_date TEXT,
                last_modified TEXT
            )
        """)

        # Create indexes for performance
        conn.execute(
            "CREATE INDEX IF NOT EXISTS idx_repositories_name ON repositories(name)"
        )
        conn.execute(
            "CREATE INDEX IF NOT EXISTS idx_repositories_url ON repositories(url)"
        )
        conn.execute(
            "CREATE INDEX IF NOT EXISTS idx_repositories_type ON repositories(type)"
        )
        conn.execute(
            "CREATE INDEX IF NOT EXISTS idx_repositories_enabled ON repositories(enabled)"
        )
        conn.execute(
            "CREATE INDEX IF NOT EXISTS idx_repositories_last_revision ON repositories(last_revision)"
        )

        self.logger.info("Repositories table created successfully")

    def _migration_013_create_notifications_tables(self, conn: sqlite3.Connection):
        """Migration 013: Create notification tables to consolidate notification database"""
        self.logger.info("Running migration 13: Creating notification tables")

        # Create notification_events table
        conn.execute("""
            CREATE TABLE IF NOT EXISTS notification_events (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                event_id TEXT UNIQUE NOT NULL,
                category TEXT NOT NULL,
                severity TEXT NOT NULL,
                title TEXT NOT NULL,
                message TEXT NOT NULL,
                repository_id TEXT,
                user_id TEXT,
                commit_info TEXT,  -- JSON string
                metadata TEXT,     -- JSON string
                timestamp TEXT NOT NULL,
                processed BOOLEAN DEFAULT FALSE,
                email_sent BOOLEAN DEFAULT FALSE,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP
            )
        """)

        # Create notification_statistics table
        conn.execute("""
            CREATE TABLE IF NOT EXISTS notification_statistics (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                date TEXT NOT NULL,
                category TEXT NOT NULL,
                severity TEXT NOT NULL,
                count INTEGER DEFAULT 0,
                repository_id TEXT,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(date, category, severity, repository_id)
            )
        """)

        # Create indexes for notification tables
        conn.execute(
            "CREATE INDEX IF NOT EXISTS idx_notification_events_category ON notification_events(category)"
        )
        conn.execute(
            "CREATE INDEX IF NOT EXISTS idx_notification_events_severity ON notification_events(severity)"
        )
        conn.execute(
            "CREATE INDEX IF NOT EXISTS idx_notification_events_repository ON notification_events(repository_id)"
        )
        conn.execute(
            "CREATE INDEX IF NOT EXISTS idx_notification_events_timestamp ON notification_events(timestamp)"
        )
        conn.execute(
            "CREATE INDEX IF NOT EXISTS idx_notification_events_processed ON notification_events(processed)"
        )

        conn.execute(
            "CREATE INDEX IF NOT EXISTS idx_notification_stats_date ON notification_statistics(date)"
        )
        conn.execute(
            "CREATE INDEX IF NOT EXISTS idx_notification_stats_category ON notification_statistics(category)"
        )
        conn.execute(
            "CREATE INDEX IF NOT EXISTS idx_notification_stats_repository ON notification_statistics(repository_id)"
        )

        self.logger.info("Notification tables created successfully")
