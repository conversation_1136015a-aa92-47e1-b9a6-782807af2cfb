#!/usr/bin/env python3
"""
Data models for RepoSense AI application
Contains dataclasses for configuration and commit information
"""

import secrets
import uuid
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from typing import Dict, List, Optional


class UserRole(Enum):
    """User roles for access control and notification preferences"""

    ADMIN = "admin"
    MANAGER = "manager"
    DEVELOPER = "developer"
    VIEWER = "viewer"


class HistoricalScanStatus(Enum):
    """Historical scan status enumeration"""

    NOT_STARTED = "not_started"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class NotificationCategory(Enum):
    """Categories of notifications users can subscribe to"""

    COMMITS = "commits"  # New commits (current functionality)
    SYSTEM_HEALTH = "system_health"  # Service status, failures
    REPOSITORY_MGMT = "repository_mgmt"  # Repo changes, discoveries
    SECURITY_ALERTS = "security_alerts"  # High-risk changes, security issues
    PROCESSING_STATUS = "processing_status"  # Document processing events
    MAINTENANCE = "maintenance"  # Performance, disk space warnings


class RepositoryRelationshipType(Enum):
    """Types of relationships between users and repositories"""

    OWNER = "owner"  # Full administrative control
    MAINTAINER = "maintainer"  # Can modify settings, assign users
    DEVELOPER = "developer"  # Active contributor, gets all commit notifications
    REVIEWER = "reviewer"  # Code review responsibilities, gets high-risk notifications
    OBSERVER = "observer"  # Interested party, gets summary notifications
    SUBSCRIBER = "subscriber"  # Opted-in for notifications


@dataclass
class HistoricalScanConfig:
    """Configuration for historical scanning of a repository"""

    enabled: bool = False

    # Revision range scanning
    scan_by_revision: bool = True
    start_revision: Optional[int] = None
    end_revision: Optional[int] = None

    # Date range scanning
    scan_by_date: bool = False
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None

    # Scanning preferences
    batch_size: int = 10  # Number of revisions to process in each batch
    include_merge_commits: bool = True
    skip_large_commits: bool = False  # Skip commits with >100 files changed
    max_files_per_commit: int = 100
    force_rescan: bool = False  # Allow re-scanning of existing revisions

    # Progress tracking
    last_scanned_revision: Optional[int] = None
    scan_status: HistoricalScanStatus = HistoricalScanStatus.NOT_STARTED
    scan_started_at: Optional[datetime] = None
    scan_completed_at: Optional[datetime] = None
    total_revisions: Optional[int] = None
    processed_revisions: int = 0
    failed_revisions: int = 0
    error_message: Optional[str] = None

    # Analysis preferences
    generate_documentation: bool = True
    analyze_code_review: bool = True
    analyze_documentation_impact: bool = True


@dataclass
class RepositoryNotificationPreferences:
    """Notification preferences specific to a repository relationship"""

    # Event categories for this repository
    enabled_categories: List[NotificationCategory] = field(default_factory=list)

    # Commit-specific preferences
    notify_on_own_commits: bool = False  # Get notifications for your own commits
    notify_on_high_risk_only: bool = False  # Only high-risk commits
    notify_on_specific_paths: List[str] = field(
        default_factory=list
    )  # Specific file paths/patterns

    # Processing preferences
    notify_on_processing_failures: bool = True
    notify_on_scan_completion: bool = False

    # Delivery preferences
    immediate_notification: bool = True
    digest_frequency: str = "never"  # "never", "daily", "weekly"

    @classmethod
    def get_defaults_for_relationship(
        cls, relationship_type: RepositoryRelationshipType
    ) -> "RepositoryNotificationPreferences":
        """Get default preferences based on relationship type"""
        if relationship_type == RepositoryRelationshipType.OWNER:
            return cls(
                enabled_categories=[
                    NotificationCategory.COMMITS,
                    NotificationCategory.SECURITY_ALERTS,
                    NotificationCategory.PROCESSING_STATUS,
                    NotificationCategory.REPOSITORY_MGMT,
                ],
                notify_on_processing_failures=True,
                notify_on_scan_completion=True,
            )
        elif relationship_type == RepositoryRelationshipType.MAINTAINER:
            return cls(
                enabled_categories=[
                    NotificationCategory.COMMITS,
                    NotificationCategory.SECURITY_ALERTS,
                    NotificationCategory.PROCESSING_STATUS,
                    NotificationCategory.REPOSITORY_MGMT,
                ],
                notify_on_processing_failures=True,
                notify_on_scan_completion=True,
            )
        elif relationship_type == RepositoryRelationshipType.DEVELOPER:
            return cls(
                enabled_categories=[
                    NotificationCategory.COMMITS,
                    NotificationCategory.SECURITY_ALERTS,
                ],
                notify_on_own_commits=False,
                notify_on_processing_failures=True,
            )
        elif relationship_type == RepositoryRelationshipType.REVIEWER:
            return cls(
                enabled_categories=[NotificationCategory.SECURITY_ALERTS],
                notify_on_high_risk_only=True,
                notify_on_processing_failures=False,
            )
        elif relationship_type == RepositoryRelationshipType.OBSERVER:
            return cls(
                enabled_categories=[NotificationCategory.COMMITS],
                digest_frequency="daily",
                immediate_notification=False,
            )
        else:  # SUBSCRIBER
            return cls(
                enabled_categories=[NotificationCategory.COMMITS],
                notify_on_high_risk_only=False,
            )


@dataclass
class RepositoryUserRelationship:
    """Defines the relationship between a user and repository"""

    user_id: str
    repository_id: str
    relationship_type: RepositoryRelationshipType

    # Notification preferences for this specific relationship
    notification_preferences: RepositoryNotificationPreferences = field(
        default_factory=RepositoryNotificationPreferences
    )

    # Metadata
    assigned_by: Optional[str] = None  # User ID who made the assignment
    assigned_date: Optional[str] = None
    last_modified: Optional[str] = None

    # Access control
    can_modify_settings: bool = False
    can_assign_users: bool = False
    can_view_sensitive_data: bool = True

    def __post_init__(self):
        """Set default notification preferences based on relationship type"""
        if not self.notification_preferences.enabled_categories:
            self.notification_preferences = (
                RepositoryNotificationPreferences.get_defaults_for_relationship(
                    self.relationship_type
                )
            )


@dataclass
class NotificationPreferences:
    """Global notification preferences for users"""

    # Category subscriptions
    enabled_categories: List[NotificationCategory] = field(default_factory=list)

    # Severity filtering
    min_severity: str = "INFO"  # DEBUG, INFO, WARNING, ERROR, CRITICAL

    # Delivery preferences
    email_enabled: bool = True
    email_digest: bool = False  # Send daily digest instead of immediate
    digest_time: str = "09:00"  # Time for daily digest

    @classmethod
    def get_defaults_for_role(cls, role: UserRole) -> "NotificationPreferences":
        """Get default notification preferences based on user role"""
        if role == UserRole.ADMIN:
            return cls(
                enabled_categories=[
                    NotificationCategory.SYSTEM_HEALTH,
                    NotificationCategory.SECURITY_ALERTS,
                    NotificationCategory.MAINTENANCE,
                ],
                min_severity="WARNING",
            )
        elif role == UserRole.MANAGER:
            return cls(
                enabled_categories=[
                    NotificationCategory.COMMITS,
                    NotificationCategory.SECURITY_ALERTS,
                    NotificationCategory.PROCESSING_STATUS,
                ],
                min_severity="INFO",
            )
        elif role == UserRole.DEVELOPER:
            return cls(
                enabled_categories=[NotificationCategory.COMMITS], min_severity="INFO"
            )
        else:  # VIEWER
            return cls(enabled_categories=[], min_severity="ERROR")


@dataclass
class User:
    """User configuration with details and preferences"""

    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    username: str = ""
    email: str = ""
    full_name: str = ""
    role: UserRole = UserRole.DEVELOPER
    enabled: bool = True

    # Enhanced notification preferences
    notification_preferences: NotificationPreferences = field(
        default_factory=NotificationPreferences
    )

    # Legacy notification preferences (for backward compatibility)
    receive_all_notifications: bool = (
        False  # If True, receives notifications for all repositories
    )
    repository_subscriptions: List[str] = field(
        default_factory=list
    )  # Repository IDs user is subscribed to

    # Contact details
    phone: Optional[str] = None
    department: Optional[str] = None

    # Metadata
    created_date: Optional[str] = None
    last_modified: Optional[str] = None

    def __post_init__(self):
        if not self.username and self.email:
            # Generate username from email if not provided
            self.username = self.email.split("@")[0]

        # Set default notification preferences based on role if not already set
        if not self.notification_preferences.enabled_categories:
            self.notification_preferences = (
                NotificationPreferences.get_defaults_for_role(self.role)
            )


@dataclass
class RepositoryConfig:
    """Configuration for a single repository"""

    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    name: str = ""
    url: str = ""
    type: str = "svn"  # Repository type: 'svn' or 'git'
    username: Optional[str] = None
    password: Optional[str] = None
    last_revision: int = 0
    last_commit_date: Optional[datetime] = (
        None  # When the revision was committed to source control
    )
    last_processed_time: Optional[datetime] = (
        None  # When RepoSense AI processed the revision
    )
    enabled: bool = True

    # Branch/path selection
    branch_path: Optional[str] = (
        None  # Specific branch path to monitor (e.g., "trunk", "branches/feature-x")
    )
    monitor_all_branches: bool = (
        False  # If True, monitor all branches; if False, monitor only branch_path
    )

    # User associations
    assigned_users: List[str] = field(
        default_factory=list
    )  # User IDs assigned to this repository

    # Legacy email support (for backward compatibility)
    email_recipients: List[str] = field(default_factory=list)  # Direct email addresses

    # Historical scanning configuration
    historical_scan: HistoricalScanConfig = field(default_factory=HistoricalScanConfig)

    # Product documentation configuration
    product_documentation_files: List[str] = field(
        default_factory=list
    )  # Specific files to reference for product documentation context

    # Risk Assessment Aggressiveness Settings
    risk_aggressiveness: str = (
        "BALANCED"  # CONSERVATIVE, BALANCED, AGGRESSIVE, VERY_AGGRESSIVE
    )
    risk_description: str = (
        ""  # User description of why this aggressiveness level was chosen
    )

    # Enhanced user relationships (replaces assigned_users)
    user_relationships: List[RepositoryUserRelationship] = field(default_factory=list)

    # Repository-level notification settings
    default_notification_preferences: RepositoryNotificationPreferences = field(
        default_factory=RepositoryNotificationPreferences
    )

    # Team/group assignments
    team_assignments: List[str] = field(default_factory=list)  # Team IDs

    # Path-based notifications
    path_watchers: Dict[str, List[str]] = field(
        default_factory=dict
    )  # path_pattern -> [user_ids]

    def get_users_by_relationship(
        self, relationship_type: RepositoryRelationshipType
    ) -> List[str]:
        """Get user IDs with a specific relationship type"""
        return [
            rel.user_id
            for rel in self.user_relationships
            if rel.relationship_type == relationship_type
        ]

    def get_user_relationship(
        self, user_id: str
    ) -> Optional[RepositoryUserRelationship]:
        """Get the relationship for a specific user"""
        for rel in self.user_relationships:
            if rel.user_id == user_id:
                return rel
        return None

    def add_user_relationship(
        self,
        user_id: str,
        relationship_type: RepositoryRelationshipType,
        assigned_by: Optional[str] = None,
        notification_preferences: Optional[RepositoryNotificationPreferences] = None,
    ) -> RepositoryUserRelationship:
        """Add or update a user relationship"""
        # Remove existing relationship if any
        self.user_relationships = [
            rel for rel in self.user_relationships if rel.user_id != user_id
        ]

        # Create new relationship with custom or default preferences
        if notification_preferences is None:
            notification_preferences = (
                RepositoryNotificationPreferences.get_defaults_for_relationship(
                    relationship_type
                )
            )

        relationship = RepositoryUserRelationship(
            user_id=user_id,
            repository_id=self.id,
            relationship_type=relationship_type,
            notification_preferences=notification_preferences,
            assigned_by=assigned_by,
            assigned_date=datetime.now().isoformat(),
        )

        self.user_relationships.append(relationship)
        return relationship

    def remove_user_relationship(self, user_id: str) -> bool:
        """Remove a user relationship"""
        original_count = len(self.user_relationships)
        self.user_relationships = [
            rel for rel in self.user_relationships if rel.user_id != user_id
        ]
        return len(self.user_relationships) < original_count

    def add_path_watcher(self, path_pattern: str, user_ids: List[str]):
        """Add users to watch specific paths in the repository"""
        if path_pattern not in self.path_watchers:
            self.path_watchers[path_pattern] = []

        for user_id in user_ids:
            if user_id not in self.path_watchers[path_pattern]:
                self.path_watchers[path_pattern].append(user_id)

    def get_path_watchers(self, changed_paths: List[str]) -> List[str]:
        """Get users who should be notified based on changed paths"""
        import fnmatch

        watchers = set()

        for path in changed_paths:
            for pattern, user_ids in self.path_watchers.items():
                if fnmatch.fnmatch(path, pattern):
                    watchers.update(user_ids)

        return list(watchers)

    def __post_init__(self):
        if not self.name and self.url:
            # Generate a default name from URL
            self.name = self.url.split("/")[-1] or self.url.split("/")[-2]

        # Validate risk aggressiveness level
        valid_levels = ["CONSERVATIVE", "BALANCED", "AGGRESSIVE", "VERY_AGGRESSIVE"]
        if self.risk_aggressiveness not in valid_levels:
            self.risk_aggressiveness = "BALANCED"


@dataclass
class CommitInfo:
    """Data class for repository commit information"""

    revision: str
    author: str
    date: str
    message: str
    changed_paths: List[str]
    diff: str
    repository_id: str = ""
    repository_name: str = ""


@dataclass
class ServerConfig:
    """Configuration for SVN/Git server connection"""

    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    name: str = (
        "default"  # User-defined friendly name (e.g., "main-server", "dev-server")
    )
    description: str = ""  # Optional description
    base_url: str = ""  # Base URL for the server (e.g., "http://sundc:81")
    default_username: Optional[str] = (
        None  # Default username for repositories on this server
    )
    default_password: Optional[str] = (
        None  # Default password for repositories on this server
    )
    enabled: bool = True


@dataclass
class Team:
    """Team/group of users for easier repository assignment"""

    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    name: str = ""
    description: str = ""
    members: List[str] = field(default_factory=list)  # User IDs
    default_relationship_type: RepositoryRelationshipType = (
        RepositoryRelationshipType.DEVELOPER
    )
    enabled: bool = True

    # Team-level notification preferences
    team_notification_preferences: RepositoryNotificationPreferences = field(
        default_factory=RepositoryNotificationPreferences
    )

    # Metadata
    created_date: Optional[str] = None
    last_modified: Optional[str] = None

    def add_member(self, user_id: str) -> bool:
        """Add a user to the team"""
        if user_id not in self.members:
            self.members.append(user_id)
            self.last_modified = datetime.now().isoformat()
            return True
        return False

    def remove_member(self, user_id: str) -> bool:
        """Remove a user from the team"""
        if user_id in self.members:
            self.members.remove(user_id)
            self.last_modified = datetime.now().isoformat()
            return True
        return False


@dataclass
class Config:
    """Configuration data class"""

    # Server configuration
    server: ServerConfig = field(
        default_factory=ServerConfig
    )  # Single server for now, can expand to list later

    # Team management
    teams: List[Team] = field(default_factory=list)

    # AI and monitoring settings
    ollama_host: str = (
        "http://localhost:11434"  # Default to localhost for local development
    )
    ollama_model: str = "qwen3"  # Default model for technical analysis

    # Specialized AI models for different tasks
    ollama_model_documentation: Optional[str] = (
        None  # Model for product documentation analysis
    )
    ollama_model_code_review: Optional[str] = None  # Model for code review analysis
    ollama_model_risk_assessment: Optional[str] = None  # Model for risk assessment

    # Timeout settings for AI operations
    ollama_timeout_base: int = 300  # Base timeout in seconds (5 minutes)
    ollama_timeout_connection: int = 30  # Connection test timeout
    ollama_timeout_embeddings: int = 60  # Embeddings timeout

    # Enhanced prompt settings
    use_enhanced_prompts: bool = True  # Enable enhanced contextual prompts
    enhanced_prompts_fallback: bool = (
        True  # Fall back to basic prompts if enhanced fail
    )

    check_interval: int = 300  # seconds (5 minutes - reasonable default)

    # Startup behavior settings
    skip_initial_scan: bool = (
        False  # Skip initial document scan on startup to reduce LLM activity
    )
    cleanup_orphaned_documents: bool = False  # Clean up orphaned documents on startup (disabled by default to preserve data)

    # SVN server settings (for repository discovery)
    svn_server_url: Optional[str] = None
    svn_server_username: Optional[str] = None
    svn_server_password: Optional[str] = None
    svn_server_type: str = "auto"  # auto, visualsvn, apache, apache_dav, standard

    # Email settings (defaults to MailHog for development)
    smtp_host: str = "mailhog"
    smtp_port: int = 1025
    smtp_username: Optional[str] = None
    smtp_password: Optional[str] = None
    email_from: str = "reposense-ai@localhost"
    email_recipients: List[str] = field(
        default_factory=list
    )  # Global recipients (always get emails)

    # Output settings
    output_dir: str = "/app/data/output"
    generate_docs: bool = True
    send_emails: bool = True

    # Web interface settings
    web_enabled: bool = True
    web_port: int = 5000
    web_host: str = "0.0.0.0"
    web_secret_key: str = ""
    web_log_entries: int = 300  # Number of log entries to display in web interface

    # Log cleanup and rotation settings
    log_cleanup_max_size_mb: int = 50  # Manual cleanup trigger size in MB
    log_cleanup_lines_to_keep: int = (
        1000  # Number of recent lines to keep after cleanup
    )
    log_rotation_max_size_mb: int = 10  # Automatic rotation trigger size in MB
    log_rotation_backup_count: int = 5  # Number of backup files to keep

    def __post_init__(self):
        if not self.web_secret_key:
            self.web_secret_key = secrets.token_hex(32)

    # Team management methods
    def add_team(self, team: Team) -> bool:
        """Add a team to the configuration"""
        if not any(t.id == team.id for t in self.teams):
            if not team.created_date:
                team.created_date = datetime.now().isoformat()
            self.teams.append(team)
            return True
        return False

    def get_team_by_id(self, team_id: str) -> Optional[Team]:
        """Get team by ID"""
        for team in self.teams:
            if team.id == team_id:
                return team
        return None

    def get_team_by_name(self, name: str) -> Optional[Team]:
        """Get team by name (case-insensitive)"""
        for team in self.teams:
            if team.name.lower() == name.lower():
                return team
        return None

    def remove_team(self, team_id: str) -> bool:
        """Remove a team"""
        for i, team in enumerate(self.teams):
            if team.id == team_id:
                del self.teams[i]
                return True
        return False
