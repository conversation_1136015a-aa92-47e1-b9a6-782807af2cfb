#!/usr/bin/env python3
"""
Web interface for the RepoSense AI application
Provides a Flask-based web UI for configuration and monitoring
"""

import logging
import os
import re
from typing import Optional

from config_manager import ConfigManager
from document_service import Document, DocumentService
from flask import (
    Flask,
    flash,
    jsonify,
    make_response,
    redirect,
    render_template,
    request,
    send_file,
    url_for,
)
from historical_scanner import HistoricalScanner
from markupsafe import Markup
from models import (
    Config,
    HistoricalScanConfig,
    HistoricalScanStatus,
    RepositoryConfig,
    User,
    UserRole,
)
from monitor_service import MonitorService
from repository_backends import get_backend_manager
from repository_backends.base import RepositoryBackend
from user_management_service import UserManagementService


def _parse_bool_filter(value):
    """Parse boolean filter value from string"""
    if not value or value == "":
        return None
    return value.lower() == "true"


class WebInterface:
    """Web interface for configuration and monitoring"""

    def __init__(self, monitor_service: MonitorService):
        self.monitor_service = monitor_service
        self.config_manager = monitor_service.config_manager
        self.file_manager = monitor_service.file_manager

        # Initialize new services
        self.user_service = UserManagementService(
            monitor_service.config, monitor_service.db_path
        )
        self.backend_manager = get_backend_manager()
        # Initialize unified document processor for all document processing FIRST
        from unified_document_processor import UnifiedDocumentProcessor

        self.unified_processor = UnifiedDocumentProcessor(
            output_dir=monitor_service.config.output_dir,
            db_path="/app/data/reposense.db",  # Use consolidated database
            ollama_client=monitor_service.ollama_client,
            config_manager=monitor_service.config_manager,
            max_concurrent_tasks=3,
        )
        self.unified_processor.start()

        self.document_service = DocumentService(
            monitor_service.config.output_dir,
            db_path=monitor_service.db_path,  # Use the same consolidated database
            ollama_client=monitor_service.ollama_client,
            config_manager=monitor_service.config_manager,
            unified_processor=self.unified_processor,  # Pass the unified processor
        )

        self.historical_scanner = HistoricalScanner(
            self.backend_manager,
            self.unified_processor,  # Pass unified processor instead of document processor
            monitor_service.ollama_client,
            db_path=monitor_service.db_path,  # Use the same consolidated database
            config_manager=monitor_service.config_manager,
            monitor_service=monitor_service,
            notification_manager=monitor_service.notification_manager,
        )
        self.historical_scanner.start()

        self.app = Flask(__name__)
        self.app.secret_key = monitor_service.config.web_secret_key
        # Allow encoded slashes in URLs for document IDs that contain slashes
        self.app.url_map.strict_slashes = False
        self.logger = logging.getLogger(__name__)

        # Register custom Jinja2 filters
        self.app.jinja_env.filters["markdown"] = self.markdown_to_html

        self.setup_routes()

    def markdown_to_html(self, text):
        """Convert markdown text to HTML using simple regex replacements"""
        if not text:
            return ""

        # Escape HTML characters first
        html = text.replace("&", "&amp;").replace("<", "&lt;").replace(">", "&gt;")

        # Convert markdown syntax to HTML
        # Headers
        html = re.sub(r"^# (.+)$", r"<h1>\1</h1>", html, flags=re.MULTILINE)
        html = re.sub(r"^## (.+)$", r"<h2>\1</h2>", html, flags=re.MULTILINE)
        html = re.sub(r"^### (.+)$", r"<h3>\1</h3>", html, flags=re.MULTILINE)
        html = re.sub(r"^#### (.+)$", r"<h4>\1</h4>", html, flags=re.MULTILINE)

        # Bold and italic
        html = re.sub(r"\*\*(.+?)\*\*", r"<strong>\1</strong>", html)
        html = re.sub(r"\*(.+?)\*", r"<em>\1</em>", html)

        # Code blocks
        html = re.sub(
            r"```(\w+)?\n(.*?)\n```",
            r'<pre><code class="language-\1">\2</code></pre>',
            html,
            flags=re.DOTALL,
        )
        html = re.sub(r"`(.+?)`", r"<code>\1</code>", html)

        # Tables - process before lists to avoid conflicts
        html = self._process_markdown_tables(html)

        # Lists
        html = re.sub(r"^- (.+)$", r"<li>\1</li>", html, flags=re.MULTILINE)
        html = re.sub(r"(<li>.*</li>)", r"<ul>\1</ul>", html, flags=re.DOTALL)
        html = re.sub(r"</ul>\s*<ul>", "", html)  # Merge consecutive lists

        # Line breaks
        html = re.sub(r"\n\n", "</p><p>", html)
        html = re.sub(r"\n", "<br>", html)

        # Wrap in paragraphs
        if html and not html.startswith("<"):
            html = f"<p>{html}</p>"

        return Markup(html)

    def _process_markdown_tables(self, html):
        """Process markdown tables and convert them to HTML tables with Bootstrap styling"""
        lines = html.split("\n")
        processed_lines = []
        in_table = False
        table_rows = []

        i = 0
        while i < len(lines):
            line = lines[i].strip()

            # Check if this line looks like a table row (contains |)
            if "|" in line and line.count("|") >= 2:
                # Check if next line is a separator (contains dashes and |)
                is_header = False
                if i + 1 < len(lines):
                    next_line = lines[i + 1].strip()
                    if "|" in next_line and "-" in next_line:
                        is_header = True
                        i += 1  # Skip the separator line

                if not in_table:
                    # Start new table
                    in_table = True
                    table_rows = []

                # Process table row
                cells = [cell.strip() for cell in line.split("|") if cell.strip()]
                if cells:  # Only process non-empty rows
                    if is_header:
                        table_rows.append(("th", cells))
                    else:
                        table_rows.append(("td", cells))

            else:
                # Not a table row
                if in_table:
                    # End current table and output it
                    processed_lines.append(self._render_html_table(table_rows))
                    in_table = False
                    table_rows = []

                processed_lines.append(line)

            i += 1

        # Handle table at end of content
        if in_table and table_rows:
            processed_lines.append(self._render_html_table(table_rows))

        return "\n".join(processed_lines)

    def _render_html_table(self, table_rows):
        """Render HTML table with Bootstrap styling"""
        if not table_rows:
            return ""

        html = ['<div class="table-responsive my-1">']
        html.append('<table class="table table-striped table-bordered">')

        # Separate header and body rows
        header_rows = [row for row in table_rows if row[0] == "th"]
        body_rows = [row for row in table_rows if row[0] == "td"]

        # Render header
        if header_rows:
            html.append('<thead class="table-dark">')
            for _, cells in header_rows:
                html.append("<tr>")
                for cell in cells:
                    html.append(f'<th scope="col">{cell}</th>')
                html.append("</tr>")
            html.append("</thead>")

        # Render body
        if body_rows:
            html.append("<tbody>")
            for _, cells in body_rows:
                html.append("<tr>")
                for cell in cells:
                    html.append(f"<td>{cell}</td>")
                html.append("</tr>")
            html.append("</tbody>")

        html.append("</table>")
        html.append("</div>")

        return "\n".join(html)

    def setup_routes(self):
        """Setup Flask routes"""

        @self.app.route("/health")
        def health_check():
            """Health check endpoint for Docker and load balancers"""
            try:
                # Check if monitor service is responsive
                status = self.monitor_service.get_status()

                # Check database connectivity
                try:
                    self.document_service.get_documents(limit=1)
                    db_status = "healthy"
                except Exception as e:
                    db_status = f"error: {str(e)}"

                # Check Ollama connectivity (if configured)
                ollama_status = "not_configured"
                if (
                    hasattr(self.monitor_service, "ollama_client")
                    and self.monitor_service.ollama_client
                ):
                    try:
                        models = (
                            self.monitor_service.ollama_client.get_available_models()
                        )
                        ollama_status = "healthy" if models else "no_models"
                    except Exception as e:
                        ollama_status = f"error: {str(e)}"

                health_data = {
                    "status": "healthy",
                    "timestamp": status.get("current_time", "unknown"),
                    "version": "2.1.0",
                    "services": {
                        "monitor": "healthy"
                        if status.get("is_running", False)
                        else "stopped",
                        "database": db_status,
                        "ollama": ollama_status,
                    },
                    "uptime": status.get("uptime", "unknown"),
                }

                # Return 200 if all critical services are healthy
                http_status = 200 if db_status == "healthy" else 503
                return jsonify(health_data), http_status

            except Exception as e:
                return jsonify(
                    {"status": "unhealthy", "error": str(e), "timestamp": "unknown"}
                ), 503

        @self.app.route("/test-processing-ui")
        def test_processing_ui():
            """Test page for processing feedback UI"""
            import os

            test_file = os.path.join(os.getcwd(), "test_processing_ui.html")
            if os.path.exists(test_file):
                with open(test_file, "r", encoding="utf-8") as f:
                    return f.read()
            else:
                return "Test file not found", 404

        @self.app.route("/")
        def index():
            import time

            start_time = time.time()
            status = self.monitor_service.get_status()
            load_time = time.time() - start_time
            self.logger.debug(f"Dashboard loaded in {load_time:.3f}s")
            return render_template(
                "index.html", status=status, config=self.monitor_service.config
            )

        @self.app.route("/config")
        def config_page():
            # Get available models for fallback
            available_models = self.monitor_service.get_available_models()
            return render_template(
                "config.html",
                config=self.monitor_service.config,
                available_models=available_models,
            )

        @self.app.route("/config/save", methods=["POST"])
        def save_config():
            try:
                # Update config from form data
                new_config = self.config_manager.update_config_from_form(
                    self.monitor_service.config, request.form
                )

                # Validate config
                is_valid, errors = self.config_manager.validate_config(new_config)
                if not is_valid:
                    for error in errors:
                        flash(error, "error")
                    return redirect(url_for("config_page"))

                # Update monitor service with new config
                self.monitor_service.update_config(new_config)

                flash("Configuration saved successfully!", "success")

            except Exception as e:
                flash(f"Error saving configuration: {str(e)}", "error")

            return redirect(url_for("config_page"))

        @self.app.route("/api/status")
        def api_status():
            import time

            start_time = time.time()
            status = self.monitor_service.get_status()
            load_time = time.time() - start_time
            status["load_time_ms"] = round(load_time * 1000, 2)
            return jsonify(status)

        @self.app.route("/api/performance")
        def api_performance():
            """Get performance metrics"""
            import time

            return jsonify(
                {
                    "ollama_cache_age_seconds": time.time()
                    - (self.monitor_service.ollama_cache_time or 0)
                    if self.monitor_service.ollama_cache_time
                    else None,
                    "ollama_cache_duration_seconds": self.monitor_service.ollama_cache_duration,
                    "ollama_cached_status": self.monitor_service.ollama_connected_cache,
                }
            )

        @self.app.route("/api/ollama/models")
        def api_ollama_models():
            """Get available Ollama models"""
            models = self.monitor_service.get_available_models()
            connection_status = self.monitor_service.get_ollama_connection_status()

            # Debug information
            import time

            cache_age = (
                time.time() - (self.monitor_service.ollama_cache_time or 0)
                if self.monitor_service.ollama_cache_time
                else None
            )

            return jsonify(
                {
                    "models": models,
                    "count": len(models),
                    "connected": connection_status,
                    "debug": {
                        "cache_age_seconds": cache_age,
                        "cache_duration": self.monitor_service.ollama_cache_duration,
                        "cached_status": self.monitor_service.ollama_connected_cache,
                        "ollama_host": self.monitor_service.config.ollama_host,
                    },
                }
            )

        @self.app.route("/api/ollama/test-models", methods=["POST"])
        def api_test_ollama_models():
            """Test connection to a specific Ollama host and get its models"""
            try:
                # Handle both JSON and form data
                if request.is_json:
                    data = request.get_json()
                else:
                    data = request.form.to_dict()

                self.logger.debug(f"Received test-models request: {data}")

                if not data or "ollama_host" not in data:
                    return jsonify(
                        {
                            "success": False,
                            "error": "Missing ollama_host parameter",
                            "models": [],
                            "count": 0,
                        }
                    ), 400

                ollama_host = data["ollama_host"].strip()
                if not ollama_host:
                    return jsonify(
                        {
                            "success": False,
                            "error": "Empty ollama_host parameter",
                            "models": [],
                            "count": 0,
                        }
                    ), 400

                # Create a temporary config with the test host
                from models import Config
                from ollama_client import OllamaClient

                temp_config = Config()
                temp_config.ollama_host = ollama_host
                temp_config.ollama_timeout_connection = 10  # Short timeout for testing
                temp_config.ollama_timeout_request = 30  # Add request timeout
                temp_config.ollama_model = "test"  # Add required model field

                # Create temporary client and test connection
                temp_client = OllamaClient(temp_config)
                models = temp_client.get_available_models()

                return jsonify(
                    {
                        "success": True,
                        "models": models,
                        "count": len(models),
                        "connected": True,
                        "host": ollama_host,
                    }
                )

            except Exception as e:
                self.logger.error(f"Failed to test Ollama host: {e}")
                return jsonify(
                    {
                        "success": False,
                        "error": str(e),
                        "models": [],
                        "count": 0,
                        "connected": False,
                    }
                ), 500

        @self.app.route("/api/start", methods=["POST"])
        def api_start():
            enabled_repos = self.monitor_service.repo_db.get_enabled_repositories()
            if not enabled_repos:
                return jsonify(
                    {"success": False, "error": "No repositories configured or enabled"}
                ), 400

            try:
                self.monitor_service.start_monitoring()
                self.logger.info(
                    f"Monitoring started via web interface for {len(enabled_repos)} repositories"
                )
                return jsonify(
                    {
                        "success": True,
                        "status": "started",
                        "repositories": len(enabled_repos),
                    }
                )
            except Exception as e:
                self.logger.error(f"Error starting monitoring: {e}")
                return jsonify({"success": False, "error": str(e)}), 500

        @self.app.route("/api/stop", methods=["POST"])
        def api_stop():
            try:
                self.monitor_service.stop_monitoring()
                self.logger.info("Monitoring stopped via web interface")
                return jsonify({"success": True, "status": "stopped"})
            except Exception as e:
                self.logger.error(f"Error stopping monitoring: {e}")
                return jsonify({"success": False, "error": str(e)}), 500

        @self.app.route("/api/restart", methods=["POST"])
        def api_restart():
            """Restart monitoring daemon - useful when daemon crashes"""
            try:
                enabled_repos = self.monitor_service.repo_db.get_enabled_repositories()
                if not enabled_repos:
                    return jsonify(
                        {
                            "success": False,
                            "error": "No repositories configured or enabled",
                        }
                    ), 400

                # Force stop first (even if not running)
                self.monitor_service.stop_monitoring()

                # Wait a moment for cleanup
                import time

                time.sleep(2)

                # Start fresh
                self.monitor_service.start_monitoring()

                self.logger.info(
                    f"Monitoring restarted via web interface for {len(enabled_repos)} repositories"
                )
                return jsonify(
                    {
                        "success": True,
                        "status": "restarted",
                        "repositories": len(enabled_repos),
                        "message": "Monitoring daemon restarted successfully",
                    }
                )
            except Exception as e:
                self.logger.error(f"Error restarting monitoring: {e}")
                return jsonify({"success": False, "error": str(e)}), 500

        @self.app.route("/api/check", methods=["POST"])
        def api_check():
            enabled_repos = self.monitor_service.repo_db.get_enabled_repositories()
            if not enabled_repos:
                return jsonify(
                    {"success": False, "error": "No repositories configured or enabled"}
                ), 400

            try:
                self.monitor_service.run_once()
                return jsonify(
                    {
                        "success": True,
                        "status": "check completed",
                        "repositories_checked": len(enabled_repos),
                    }
                )
            except Exception as e:
                self.logger.error(f"Error during manual check: {e}")
                return jsonify({"success": False, "error": str(e)}), 500

        @self.app.route("/logs")
        def logs_page():
            try:
                log_entries_count = self.monitor_service.config.web_log_entries
                logs = self.file_manager.read_recent_logs(log_entries_count)
                log_levels = self.file_manager.get_available_log_levels()
                return render_template(
                    "logs.html",
                    logs=logs,
                    log_entries_count=log_entries_count,
                    log_levels=log_levels,
                    config=self.monitor_service.config,
                )
            except Exception as e:
                return render_template(
                    "logs.html",
                    logs=[f"Error reading logs: {str(e)}"],
                    log_entries_count=100,
                    log_levels={},
                    config=self.monitor_service.config,
                )

        @self.app.route("/api/logs/filtered")
        def api_logs_filtered():
            """API endpoint for filtered log retrieval"""
            try:
                # Get query parameters
                log_levels = request.args.getlist("levels")  # Multi-selection support
                lines = int(
                    request.args.get(
                        "lines", self.monitor_service.config.web_log_entries
                    )
                )
                include_counts = (
                    request.args.get("include_counts", "false").lower() == "true"
                )

                # Get filtered logs
                logs = self.file_manager.read_recent_logs(
                    lines, log_levels if log_levels else None
                )

                response_data = {
                    "success": True,
                    "logs": logs,
                    "total_lines": len(logs),
                    "filtered_levels": log_levels,
                }

                # Include fresh log level counts if requested
                if include_counts:
                    response_data["log_levels"] = (
                        self.file_manager.get_available_log_levels()
                    )

                return jsonify(response_data)
            except Exception as e:
                return jsonify(
                    {"success": False, "error": str(e), "logs": [], "total_lines": 0}
                ), 500

        @self.app.route("/api/logs/cleanup", methods=["POST"])
        def api_logs_cleanup():
            """API endpoint to manually clean up log files"""
            try:
                import os

                # Use configuration value as default, allow override from request
                default_size = self.monitor_service.config.log_cleanup_max_size_mb
                max_size_mb = (
                    request.json.get("max_size_mb", default_size)
                    if request.is_json
                    else default_size
                )
                success = self.file_manager.cleanup_old_logs(max_size_mb)

                if success:
                    # Get updated file size
                    log_file = self.file_manager.get_log_file_path()
                    if os.path.exists(log_file):
                        current_size_mb = os.path.getsize(log_file) / (1024 * 1024)
                    else:
                        current_size_mb = 0

                    return jsonify(
                        {
                            "success": True,
                            "message": "Log cleanup completed successfully",
                            "current_size_mb": round(current_size_mb, 2),
                        }
                    )
                else:
                    return jsonify(
                        {"success": False, "error": "Log cleanup failed"}
                    ), 500
            except Exception as e:
                return jsonify({"success": False, "error": str(e)}), 500

        @self.app.route("/api/logs/download")
        def api_logs_download():
            """API endpoint to download the current log file"""
            try:
                import os
                from datetime import datetime

                log_file = self.file_manager.get_log_file_path()
                if not os.path.exists(log_file):
                    return jsonify({"error": "Log file not found"}), 404

                # Get file size for info
                file_size_mb = os.path.getsize(log_file) / (1024 * 1024)

                # Generate filename with timestamp
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"reposense_ai_{timestamp}.log"

                return send_file(
                    log_file,
                    as_attachment=True,
                    download_name=filename,
                    mimetype="text/plain",
                )
            except Exception as e:
                return jsonify({"error": str(e)}), 500

        @self.app.route("/api/test_ollama")
        def api_test_ollama():
            connected = self.monitor_service.ollama_client.test_connection()
            # Clear cache to ensure fresh status on next model dropdown load
            if connected:
                self.monitor_service.clear_ollama_cache()
            return jsonify({"connected": connected})

        @self.app.route("/api/test_email", methods=["POST"])
        def api_test_email():
            """Send a test email using current email configuration"""
            try:
                import smtplib
                from datetime import datetime
                from email.mime.multipart import MIMEMultipart
                from email.mime.text import MIMEText

                from email_service import EmailService

                # Get current configuration
                config = self.monitor_service.config

                # Check if email is configured
                if not config.smtp_host or not config.email_from:
                    return jsonify(
                        {
                            "success": False,
                            "error": "Email not configured. Please set SMTP Host and From Address.",
                        }
                    ), 400

                # Get recipients from global recipients and enabled users
                recipients = set()

                # Add global recipients
                recipients.update(config.email_recipients)

                # Add enabled users who receive all notifications
                for user in self.user_service.user_db.get_enabled_users():
                    if user.receive_all_notifications:
                        recipients.add(user.email)

                if not recipients:
                    return jsonify(
                        {
                            "success": False,
                            "error": "No email recipients configured. Please add recipients in Global Recipients or enable user notifications.",
                        }
                    ), 400

                # Create test email
                msg = MIMEMultipart()
                msg["From"] = config.email_from
                msg["To"] = ", ".join(recipients)
                msg["Subject"] = (
                    f"🧪 RepoSense AI Test Email - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
                )

                body = f"""
This is a test email from RepoSense AI to verify that email notifications are working correctly.

📧 Email Configuration:
• SMTP Server: {config.smtp_host}:{config.smtp_port}
• From Address: {config.email_from}
• Recipients: {len(recipients)} recipient(s)
• Test Time: {datetime.now().isoformat()}

🔧 SMTP Settings:
• Username: {"Configured" if config.smtp_username else "Not configured"}
• Password: {"Configured" if config.smtp_password else "Not configured"}
• Authentication: {"Enabled" if config.smtp_username and config.smtp_password else "Disabled"}

📬 Recipients:
{chr(10).join("• " + email for email in sorted(recipients))}

✅ If you receive this email, your email configuration is working correctly!

This test email was sent from the RepoSense AI Configuration page.

Best regards,
RepoSense AI System
                """.strip()

                msg.attach(MIMEText(body, "plain"))

                # Send email
                with smtplib.SMTP(config.smtp_host, config.smtp_port) as server:
                    if config.smtp_username and config.smtp_password:
                        server.starttls()
                        server.login(config.smtp_username, config.smtp_password)

                    server.send_message(msg)

                return jsonify(
                    {
                        "success": True,
                        "message": f"Test email sent successfully to {len(recipients)} recipient(s)",
                        "recipients": list(recipients),
                        "smtp_host": config.smtp_host,
                        "smtp_port": config.smtp_port,
                    }
                )

            except smtplib.SMTPException as e:
                return jsonify(
                    {
                        "success": False,
                        "error": f"SMTP Error: {str(e)}",
                        "type": "smtp_error",
                    }
                ), 500
            except Exception as e:
                return jsonify(
                    {
                        "success": False,
                        "error": f"Unexpected error: {str(e)}",
                        "type": "general_error",
                    }
                ), 500

        # Repository management routes
        @self.app.route("/repositories")
        def repositories_page():
            # Force reload config to get latest data
            self.monitor_service.config = (
                self.monitor_service.config_manager.load_config()
            )

            # Get filter and sort parameters
            search_query = request.args.get("search", "").strip()
            status_filter = request.args.get("status", "")  # enabled, disabled, all
            type_filter = request.args.get("type", "")  # git, svn, all
            scan_status_filter = request.args.get(
                "scan_status", ""
            )  # completed, active, failed, all
            sort_by = request.args.get(
                "sort_by", "name"
            )  # name, type, status, last_revision, last_commit_date
            sort_order = request.args.get("sort_order", "asc")  # asc, desc
            view_mode = request.args.get(
                "view_mode", "table"
            )  # table, cards, status_groups

            # Start with all repositories and merge active scan data
            repositories = []
            for repo in self.monitor_service.repo_db.get_all_repositories():
                # Create a copy of the repository to avoid modifying the original
                repo_copy = repo

                # Update scan status from active/completed scans if available
                if repo.historical_scan:
                    scan_progress = None
                    if repo.id in self.historical_scanner.active_scans:
                        scan_progress = self.historical_scanner.active_scans[repo.id]
                    elif repo.id in self.historical_scanner.completed_scans:
                        scan_progress = self.historical_scanner.completed_scans[repo.id]

                    if scan_progress:
                        # Update the historical scan with current progress
                        repo.historical_scan.scan_status = scan_progress.status
                        repo.historical_scan.processed_revisions = (
                            scan_progress.processed_revisions
                        )
                        repo.historical_scan.total_revisions = (
                            scan_progress.total_revisions
                        )

                repositories.append(repo_copy)

            # Apply search filter
            if search_query:
                repositories = [
                    repo
                    for repo in repositories
                    if search_query.lower() in repo.name.lower()
                    or search_query.lower() in repo.url.lower()
                    or (repo.username and search_query.lower() in repo.username.lower())
                ]

            # Apply status filter
            if status_filter == "enabled":
                repositories = [repo for repo in repositories if repo.enabled]
            elif status_filter == "disabled":
                repositories = [repo for repo in repositories if not repo.enabled]

            # Apply type filter
            if type_filter and type_filter != "all":
                repositories = [
                    repo for repo in repositories if repo.type == type_filter
                ]

            # Apply scan status filter
            if scan_status_filter and scan_status_filter != "all":
                repositories = [
                    repo
                    for repo in repositories
                    if repo.historical_scan
                    and repo.historical_scan.scan_status
                    and repo.historical_scan.scan_status.value == scan_status_filter
                ]

            # Apply sorting
            reverse_order = sort_order == "desc"
            if sort_by == "name":
                repositories.sort(key=lambda r: r.name.lower(), reverse=reverse_order)
            elif sort_by == "type":
                repositories.sort(key=lambda r: r.type, reverse=reverse_order)
            elif sort_by == "status":
                repositories.sort(key=lambda r: r.enabled, reverse=reverse_order)
            elif sort_by == "last_revision":
                repositories.sort(
                    key=lambda r: r.last_revision or 0, reverse=reverse_order
                )
            elif sort_by == "last_commit_date":
                repositories.sort(
                    key=lambda r: r.last_commit_date or "", reverse=reverse_order
                )

            # Get filter options for dropdowns
            available_types = list(
                set(
                    repo.type
                    for repo in self.monitor_service.repo_db.get_all_repositories()
                )
            )
            available_scan_statuses = list(
                set(
                    repo.historical_scan.scan_status.value
                    for repo in self.monitor_service.repo_db.get_all_repositories()
                    if repo.historical_scan and repo.historical_scan.scan_status
                )
            )

            return render_template(
                "repositories.html",
                repositories=repositories,
                config=self.monitor_service.config,
                # Current filters
                search_query=search_query,
                status_filter=status_filter,
                type_filter=type_filter,
                scan_status_filter=scan_status_filter,
                sort_by=sort_by,
                sort_order=sort_order,
                view_mode=view_mode,
                # Filter options
                available_types=available_types,
                available_scan_statuses=available_scan_statuses,
                total_count=len(repositories),
                total_repositories=len(
                    self.monitor_service.repo_db.get_all_repositories()
                ),
            )

        @self.app.route("/repositories/add", methods=["POST"])
        def add_repository():
            try:
                # Create repository from form data
                from models import RepositoryConfig

                name = request.form.get("name", "").strip()
                url = request.form.get("url", "").strip()

                if not name or not url:
                    raise ValueError("Repository name and URL are required")

                # Parse email recipients
                email_recipients = []
                if request.form.get("email_recipients"):
                    email_recipients = [
                        email.strip()
                        for email in request.form.get("email_recipients", "").split(",")
                        if email.strip()
                    ]

                repo = RepositoryConfig(
                    name=name,
                    url=url,
                    username=request.form.get("username") or None,
                    password=request.form.get("password") or None,
                    enabled=request.form.get("enabled", "off") == "on",
                    email_recipients=email_recipients,
                    risk_aggressiveness=request.form.get(
                        "risk_aggressiveness", "BALANCED"
                    ),
                    risk_description=request.form.get("risk_description", "").strip(),
                    branch_path=request.form.get("branch_path", "trunk").strip()
                    or "trunk",
                    monitor_all_branches=request.form.get("monitor_all_branches", "off")
                    == "on",
                )

                success = self.monitor_service.repo_db.create_repository(repo)
                if not success:
                    raise Exception("Failed to save repository to database")

                self.monitor_service.file_manager.setup_directories()  # Create new repo directories
                flash(f'Repository "{repo.name}" added successfully!', "success")
            except Exception as e:
                flash(f"Error adding repository: {str(e)}", "error")

            return redirect(url_for("repositories_page"))

        @self.app.route("/repositories/<repo_id>/edit", methods=["POST"])
        def edit_repository(repo_id):
            try:
                # Get existing repository
                repo = self.monitor_service.repo_db.get_repository_by_id(repo_id)
                if not repo:
                    flash("Repository not found!", "error")
                    return redirect(url_for("repositories_page"))

                # Update repository from form data
                repo.name = request.form.get("name", repo.name).strip()
                repo.url = request.form.get("url", repo.url).strip()
                repo.username = request.form.get("username") or None
                repo.password = request.form.get("password") or None
                repo.enabled = request.form.get("enabled", "off") == "on"

                # Parse email recipients
                email_recipients = []
                if request.form.get("email_recipients"):
                    email_recipients = [
                        email.strip()
                        for email in request.form.get("email_recipients", "").split(",")
                        if email.strip()
                    ]
                repo.email_recipients = email_recipients

                repo.risk_aggressiveness = request.form.get(
                    "risk_aggressiveness", repo.risk_aggressiveness or "BALANCED"
                )
                repo.risk_description = request.form.get("risk_description", "").strip()
                repo.branch_path = (
                    request.form.get("branch_path", repo.branch_path or "trunk").strip()
                    or "trunk"
                )
                repo.monitor_all_branches = (
                    request.form.get("monitor_all_branches", "off") == "on"
                )

                success = self.monitor_service.repo_db.update_repository(repo)
                if success:
                    flash("Repository updated successfully!", "success")
                else:
                    flash("Failed to update repository!", "error")
            except Exception as e:
                flash(f"Error updating repository: {str(e)}", "error")

            return redirect(url_for("repositories_page"))

        @self.app.route("/repositories/<repo_id>/delete", methods=["POST"])
        def delete_repository(repo_id):
            try:
                repo = self.monitor_service.config.get_repository_by_id(repo_id)
                if repo:
                    repo_name = repo.name
                    if self.monitor_service.config.remove_repository(repo_id):
                        self.monitor_service.save_config()
                        flash(
                            f'Repository "{repo_name}" deleted successfully!', "success"
                        )
                    else:
                        flash("Failed to delete repository!", "error")
                else:
                    flash("Repository not found!", "error")
            except Exception as e:
                flash(f"Error deleting repository: {str(e)}", "error")

            return redirect(url_for("repositories_page"))

        @self.app.route("/repositories/bulk-action", methods=["POST"])
        def bulk_repository_action():
            """Handle bulk actions on repositories"""
            try:
                action = request.form.get("action")
                selected_repos = request.form.getlist("selected_repos")

                if not selected_repos:
                    flash("No repositories selected", "warning")
                    return redirect(url_for("repositories_page"))

                success_count = 0
                error_count = 0

                for repo_id in selected_repos:
                    try:
                        repo = self.monitor_service.config.get_repository_by_id(repo_id)
                        if not repo:
                            error_count += 1
                            continue

                        if action == "enable":
                            repo.enabled = True
                            success_count += 1
                        elif action == "disable":
                            repo.enabled = False
                            success_count += 1
                        elif action == "delete":
                            self.monitor_service.config.remove_repository(repo_id)
                            success_count += 1
                        elif action == "start_scan":
                            # Create scan configuration from form parameters
                            try:
                                scan_config = self._create_bulk_scan_config(
                                    request.form
                                )

                                # Handle revision count for "last N revisions" quick selection
                                revision_count = request.form.get("scan_revision_count")
                                if (
                                    revision_count
                                    and revision_count.strip()
                                    and scan_config.scan_by_revision
                                ):
                                    scan_config = self._apply_revision_count_to_repo(
                                        scan_config, repo, int(revision_count)
                                    )

                                # Enable historical scan for this repository if not already enabled
                                if not repo.historical_scan:
                                    repo.historical_scan = HistoricalScanConfig()

                                # Queue historical scan with the new configuration
                                queued = self.historical_scanner.queue_scan(
                                    repo, scan_config
                                )
                                if queued:
                                    success_count += 1
                                    self.logger.info(
                                        f"Started bulk scan for {repo.name} with custom configuration"
                                    )
                                else:
                                    # Check if there's a specific error message in completed_scans
                                    if (
                                        repo.id
                                        in self.historical_scanner.completed_scans
                                    ):
                                        progress = (
                                            self.historical_scanner.completed_scans[
                                                repo.id
                                            ]
                                        )
                                        if progress.error_message:
                                            self.logger.warning(
                                                f"Failed to start scan for {repo.name}: {progress.error_message}"
                                            )
                                    error_count += 1
                            except Exception as e:
                                self.logger.error(
                                    f"Error creating bulk scan configuration for {repo.name}: {e}"
                                )
                                error_count += 1
                        elif action == "stop_scan":
                            cancelled = self.historical_scanner.cancel_scan(repo_id)
                            if cancelled:
                                success_count += 1
                            else:
                                error_count += 1
                        elif action == "reset_status":
                            if repo.historical_scan:
                                # Reset scan status and progress
                                repo.historical_scan.scan_status = (
                                    HistoricalScanStatus.NOT_STARTED
                                )
                                repo.historical_scan.last_scanned_revision = None
                                repo.historical_scan.scan_started_at = None
                                repo.historical_scan.scan_completed_at = None
                                repo.historical_scan.processed_revisions = 0
                                repo.historical_scan.failed_revisions = 0
                                repo.historical_scan.total_revisions = None
                                repo.historical_scan.error_message = None

                                # Also remove from scanner's completed scans if present
                                if repo_id in self.historical_scanner.completed_scans:
                                    del self.historical_scanner.completed_scans[repo_id]

                                success_count += 1
                                self.logger.info(
                                    f"Reset scan status for repository {repo.name}"
                                )
                            else:
                                self.logger.warning(
                                    f"Cannot reset scan status for {repo.name}: No historical scan configuration"
                                )
                                error_count += 1
                        else:
                            error_count += 1

                    except Exception as e:
                        self.logger.error(
                            f"Error processing bulk action {action} for repo {repo_id}: {e}"
                        )
                        error_count += 1

                # Save configuration if any changes were made
                if action in ["enable", "disable", "delete", "reset_status"]:
                    self.monitor_service.save_config()

                # Show results
                if success_count > 0:
                    if action == "reset_status":
                        flash(
                            f"Successfully reset scan status for {success_count} repositories. You can now start new scans.",
                            "success",
                        )
                    elif action == "start_scan":
                        flash(
                            f"Successfully started scan for {success_count} repositories",
                            "success",
                        )
                    elif action == "stop_scan":
                        flash(
                            f"Successfully stopped scan for {success_count} repositories",
                            "success",
                        )
                    elif action == "enable":
                        flash(
                            f"Successfully enabled {success_count} repositories",
                            "success",
                        )
                    elif action == "disable":
                        flash(
                            f"Successfully disabled {success_count} repositories",
                            "success",
                        )
                    elif action == "delete":
                        flash(
                            f"Successfully deleted {success_count} repositories",
                            "success",
                        )
                    else:
                        flash(
                            f"Successfully processed {success_count} repositories",
                            "success",
                        )
                if error_count > 0:
                    if action == "start_scan":
                        flash(
                            f"Failed to start scan for {error_count} repositories. "
                            f"This usually means the scan is already completed or no new revisions are available. "
                            f'Check the Historical Scan page for more details or use "Reset Status" to re-scan.',
                            "warning",
                        )
                    elif action == "reset_status":
                        flash(
                            f"Failed to reset scan status for {error_count} repositories. Check that they have historical scan configured.",
                            "error",
                        )
                    else:
                        flash(f"Failed to {action} {error_count} repositories", "error")

            except Exception as e:
                flash(f"Error performing bulk action: {str(e)}", "error")

            return redirect(url_for("repositories_page"))

        @self.app.route("/api/repositories/status")
        def api_repositories_status():
            """API endpoint for real-time repository status updates"""
            try:
                # Force reload config to get latest data
                self.monitor_service.config = (
                    self.monitor_service.config_manager.load_config()
                )

                repositories_data = []
                for repo in self.monitor_service.repo_db.get_all_repositories():
                    # Get current scan progress if available
                    scan_progress = None
                    if repo.id in self.historical_scanner.active_scans:
                        scan_progress = self.historical_scanner.active_scans[repo.id]
                    elif repo.id in self.historical_scanner.completed_scans:
                        scan_progress = self.historical_scanner.completed_scans[repo.id]

                    # Build repository data
                    repo_data = {
                        "id": repo.id,
                        "name": repo.name,
                        "enabled": repo.enabled,
                        "historical_scan": None,
                    }

                    if repo.historical_scan:
                        # Update scan status from active/completed scans if available
                        current_status = repo.historical_scan.scan_status
                        processed_revisions = repo.historical_scan.processed_revisions
                        total_revisions = repo.historical_scan.total_revisions

                        if scan_progress:
                            current_status = scan_progress.status
                            processed_revisions = scan_progress.processed_revisions
                            total_revisions = scan_progress.total_revisions

                        repo_data["historical_scan"] = {
                            "enabled": repo.historical_scan.enabled,
                            "scan_status": {
                                "value": current_status.value
                                if hasattr(current_status, "value")
                                else current_status
                            },
                            "processed_revisions": processed_revisions,
                            "total_revisions": total_revisions,
                            "last_scanned_revision": repo.historical_scan.last_scanned_revision,
                        }

                    repositories_data.append(repo_data)

                return jsonify(repositories_data)

            except Exception as e:
                self.logger.error(f"Error getting repository status: {e}")
                return jsonify({"error": str(e)}), 500

        @self.app.route("/api/repositories")
        def api_repositories():
            return jsonify(
                {
                    "repositories": [
                        {
                            "id": repo.id,
                            "name": repo.name,
                            "url": repo.url,
                            "enabled": repo.enabled,
                            "last_revision": repo.last_revision,
                        }
                        for repo in self.monitor_service.repo_db.get_all_repositories()
                    ]
                }
            )

        @self.app.route("/api/repositories/<repo_id>/revisions")
        def api_repository_revisions(repo_id):
            """Get available revisions for a repository"""
            try:
                # Find repository
                repo = self.monitor_service.repo_db.get_repository_by_id(repo_id)

                if not repo:
                    return jsonify({"error": "Repository not found"}), 404

                # Get backend
                backend = self.backend_manager.get_backend_for_repository(repo, None)
                if not backend:
                    return jsonify(
                        {"error": "No backend available for repository"}
                    ), 500

                # Get latest revision
                latest_revision = backend.get_latest_revision(repo)
                if not latest_revision:
                    return jsonify(
                        {"error": "Could not determine latest revision"}
                    ), 500

                # Get actual available revisions from the repository
                # Limit to reasonable number for UI performance (last 1000 revisions)
                available_revisions = backend.get_all_available_revisions(
                    repo, limit=1000
                )

                if not available_revisions:
                    return jsonify({"error": "No revisions found in repository"}), 500

                try:
                    # Try to parse as numeric revisions (SVN)
                    latest_num = int(latest_revision)

                    # Convert to the format expected by the UI
                    revisions = []
                    for rev in available_revisions:
                        rev_num = int(rev)
                        revisions.append({"number": rev_num, "display": f"r{rev_num}"})

                    return jsonify(
                        {
                            "revisions": revisions,
                            "latest_revision": latest_num,
                            "total_count": len(revisions),
                        }
                    )

                except ValueError:
                    # Non-numeric revision system (like Git)
                    revisions = []
                    for rev in available_revisions:
                        revisions.append(
                            {
                                "number": rev,
                                "display": rev[:8]
                                if len(rev) > 8
                                else rev,  # Show short hash for Git
                            }
                        )

                    return jsonify(
                        {
                            "revisions": revisions,
                            "latest_revision": latest_revision,
                            "total_count": len(revisions),
                        }
                    )

            except Exception as e:
                self.logger.error(
                    f"Error getting revisions for repository {repo_id}: {e}"
                )
                return jsonify({"error": f"Error getting revisions: {str(e)}"}), 500

        # User Management Routes
        @self.app.route("/users")
        def users_page():
            users = self.user_service.user_db.get_all_users()
            repositories = self.monitor_service.repo_db.get_all_repositories()
            user_roles = [role.value for role in UserRole]
            return render_template(
                "users.html",
                users=users,
                repositories=repositories,
                user_roles=user_roles,
            )

        @self.app.route("/users/add", methods=["POST"])
        def add_user():
            try:
                success, message, user = self.user_service.create_user(
                    username=request.form.get("username", "").strip(),
                    email=request.form.get("email", "").strip(),
                    full_name=request.form.get("full_name", "").strip(),
                    role=UserRole(request.form.get("role", "developer")),
                    phone=request.form.get("phone", "").strip() or None,
                    department=request.form.get("department", "").strip() or None,
                    receive_all_notifications=request.form.get(
                        "receive_all_notifications"
                    )
                    == "on",
                )

                if success:
                    self.config_manager.save_config(self.monitor_service.config)
                    # Reload config to ensure monitor service has the latest data
                    self.monitor_service.config = self.config_manager.load_config()
                    # Update user service with new config
                    self.user_service = UserManagementService(
                        self.monitor_service.config, self.monitor_service.db_path
                    )
                    flash(message, "success")
                else:
                    flash(message, "error")

            except Exception as e:
                flash(f"Error adding user: {str(e)}", "error")

            return redirect(url_for("users_page"))

        @self.app.route("/api/users/add", methods=["POST"])
        def api_add_user():
            """API endpoint for adding users via AJAX"""
            try:
                success, message, user = self.user_service.create_user(
                    username=request.form.get("username", "").strip(),
                    email=request.form.get("email", "").strip(),
                    full_name=request.form.get("full_name", "").strip(),
                    role=UserRole(request.form.get("role", "developer")),
                    phone=request.form.get("phone", "").strip() or None,
                    department=request.form.get("department", "").strip() or None,
                    receive_all_notifications=request.form.get(
                        "receive_all_notifications"
                    )
                    == "on",
                )

                if success:
                    self.config_manager.save_config(self.monitor_service.config)
                    # Reload config to ensure monitor service has the latest data
                    self.monitor_service.config = self.config_manager.load_config()
                    # Update user service with new config
                    self.user_service = UserManagementService(
                        self.monitor_service.config, self.monitor_service.db_path
                    )
                    return jsonify(
                        {
                            "success": True,
                            "message": message,
                            "user_id": user.id if user else None,
                        }
                    )
                else:
                    return jsonify({"success": False, "message": message}), 400

            except Exception as e:
                self.logger.error(f"Error adding user via API: {str(e)}")
                return jsonify(
                    {"success": False, "message": f"Error adding user: {str(e)}"}
                ), 500

        @self.app.route("/users/update/<user_id>", methods=["POST"])
        def update_user(user_id):
            try:
                update_data = {
                    "username": request.form.get("username", "").strip(),
                    "email": request.form.get("email", "").strip(),
                    "full_name": request.form.get("full_name", "").strip(),
                    "role": request.form.get("role", "developer"),
                    "phone": request.form.get("phone", "").strip() or None,
                    "department": request.form.get("department", "").strip() or None,
                    "enabled": request.form.get("enabled") == "on",
                    "receive_all_notifications": request.form.get(
                        "receive_all_notifications"
                    )
                    == "on",
                }

                success, message = self.user_service.update_user(user_id, **update_data)

                if success:
                    self.config_manager.save_config(self.monitor_service.config)
                    # Reload config to ensure monitor service has the latest data
                    self.monitor_service.config = self.config_manager.load_config()
                    # Update user service with new config
                    self.user_service = UserManagementService(
                        self.monitor_service.config, self.monitor_service.db_path
                    )
                    flash(message, "success")
                else:
                    flash(message, "error")

            except Exception as e:
                flash(f"Error updating user: {str(e)}", "error")

            return redirect(url_for("users_page"))

        @self.app.route("/users/delete/<user_id>", methods=["POST"])
        def delete_user(user_id):
            try:
                success, message = self.user_service.delete_user(user_id)

                if success:
                    self.config_manager.save_config(self.monitor_service.config)
                    # Reload config to ensure monitor service has the latest data
                    self.monitor_service.config = self.config_manager.load_config()
                    # Update user service with new config
                    self.user_service = UserManagementService(
                        self.monitor_service.config, self.monitor_service.db_path
                    )
                    flash(message, "success")
                else:
                    flash(message, "error")

            except Exception as e:
                flash(f"Error deleting user: {str(e)}", "error")

            return redirect(url_for("users_page"))

        # Enhanced Notification Management Routes
        @self.app.route("/users/<user_id>/notifications")
        def user_notifications_page(user_id):
            """Page for managing user notification preferences"""
            user = self.user_service.user_db.get_user_by_id(user_id)
            if not user:
                flash("User not found", "error")
                return redirect(url_for("users_page"))

            repositories = self.monitor_service.repo_db.get_all_repositories()
            from models import NotificationCategory, RepositoryRelationshipType

            return render_template(
                "user_notifications.html",
                user=user,
                repositories=repositories,
                notification_categories=NotificationCategory,
                relationship_types=RepositoryRelationshipType,
            )

        @self.app.route(
            "/api/users/<user_id>/notification-preferences", methods=["POST"]
        )
        def update_user_notification_preferences(user_id):
            """Update user's global notification preferences"""
            try:
                user = self.user_service.user_db.get_user_by_id(user_id)
                if not user:
                    return jsonify({"success": False, "message": "User not found"}), 404

                data = request.get_json()

                # Update global notification preferences
                from models import NotificationCategory

                enabled_categories = []
                for category_name in data.get("enabled_categories", []):
                    try:
                        enabled_categories.append(NotificationCategory(category_name))
                    except ValueError:
                        continue

                user.notification_preferences.enabled_categories = enabled_categories
                user.notification_preferences.min_severity = data.get(
                    "min_severity", "INFO"
                )
                user.notification_preferences.email_enabled = data.get(
                    "email_enabled", True
                )
                user.notification_preferences.email_digest = data.get(
                    "email_digest", False
                )
                user.notification_preferences.digest_time = data.get(
                    "digest_time", "09:00"
                )

                # Save user to database
                success = self.user_service.user_db.update_user(user)
                if not success:
                    return jsonify(
                        {"success": False, "message": "Failed to save user preferences"}
                    ), 500

                return jsonify(
                    {"success": True, "message": "Notification preferences updated"}
                )

            except Exception as e:
                self.logger.error(f"Error updating user notification preferences: {e}")
                return jsonify({"success": False, "message": str(e)}), 500

        @self.app.route(
            "/api/users/<user_id>/repository-relationships", methods=["POST"]
        )
        def update_user_repository_relationships(user_id):
            """Update user's repository relationships"""
            try:
                user = self.user_service.user_db.get_user_by_id(user_id)
                if not user:
                    return jsonify({"success": False, "message": "User not found"}), 404

                data = request.get_json()
                relationships = data.get("relationships", [])

                from models import (
                    NotificationCategory,
                    RepositoryRelationshipType,
                    RepositoryUserRelationship,
                )

                # Update each repository relationship and keep track of modified repos
                modified_repos = {}
                for rel_data in relationships:
                    repo_id = rel_data.get("repository_id")
                    relationship_type_str = rel_data.get("relationship_type")

                    if not repo_id or not relationship_type_str:
                        continue

                    # Get repository from cache or database
                    if repo_id not in modified_repos:
                        repo = self.monitor_service.repo_db.get_repository_by_id(
                            repo_id
                        )
                        if not repo:
                            continue
                        modified_repos[repo_id] = repo
                    else:
                        repo = modified_repos[repo_id]

                    try:
                        relationship_type = RepositoryRelationshipType(
                            relationship_type_str
                        )
                    except ValueError:
                        continue

                    # Remove existing relationship
                    repo.user_relationships = [
                        r for r in repo.user_relationships if r.user_id != user_id
                    ]

                    # Add new relationship if not 'none'
                    if relationship_type_str != "none":
                        # Build notification preferences first
                        prefs_data = rel_data.get("notification_preferences", {})
                        enabled_categories = []
                        for category_name in prefs_data.get("enabled_categories", []):
                            try:
                                enabled_categories.append(
                                    NotificationCategory(category_name)
                                )
                            except ValueError:
                                continue

                        # Create custom notification preferences
                        from models import RepositoryNotificationPreferences

                        custom_prefs = RepositoryNotificationPreferences(
                            enabled_categories=enabled_categories,
                            notify_on_own_commits=prefs_data.get(
                                "notify_on_own_commits", False
                            ),
                            notify_on_high_risk_only=prefs_data.get(
                                "notify_on_high_risk_only", False
                            ),
                            notify_on_specific_paths=prefs_data.get(
                                "notify_on_specific_paths", []
                            ),
                            notify_on_processing_failures=prefs_data.get(
                                "notify_on_processing_failures", True
                            ),
                            notify_on_scan_completion=prefs_data.get(
                                "notify_on_scan_completion", False
                            ),
                            immediate_notification=prefs_data.get(
                                "immediate_notification", True
                            ),
                            digest_frequency=prefs_data.get(
                                "digest_frequency", "never"
                            ),
                        )

                        # Add relationship with custom preferences
                        relationship = repo.add_user_relationship(
                            user_id,
                            relationship_type,
                            notification_preferences=custom_prefs,
                        )

                # Save all modified repositories to database
                for repo_id, repo in modified_repos.items():
                    success = self.monitor_service.repo_db.update_repository(repo)
                    if not success:
                        return jsonify(
                            {
                                "success": False,
                                "message": f"Failed to save repository {repo_id} relationships",
                            }
                        ), 500

                return jsonify(
                    {"success": True, "message": "Repository relationships updated"}
                )

            except Exception as e:
                self.logger.error(f"Error updating user repository relationships: {e}")
                return jsonify({"success": False, "message": str(e)}), 500

        @self.app.route("/api/repositories/<repo_id>/user-relationships")
        def get_repository_user_relationships(repo_id):
            """Get all user relationships for a repository"""
            try:
                repo = self.monitor_service.repo_db.get_repository_by_id(repo_id)
                if not repo:
                    return jsonify(
                        {"success": False, "message": "Repository not found"}
                    ), 404

                relationships = []
                for rel in repo.user_relationships:
                    user = self.user_service.user_db.get_user_by_id(rel.user_id)
                    if user:
                        relationships.append(
                            {
                                "user_id": rel.user_id,
                                "username": user.username,
                                "email": user.email,
                                "relationship_type": rel.relationship_type.value,
                                "notification_preferences": {
                                    "enabled_categories": [
                                        cat.value
                                        for cat in rel.notification_preferences.enabled_categories
                                    ],
                                    "notify_on_own_commits": rel.notification_preferences.notify_on_own_commits,
                                    "notify_on_high_risk_only": rel.notification_preferences.notify_on_high_risk_only,
                                    "notify_on_specific_paths": rel.notification_preferences.notify_on_specific_paths,
                                    "immediate_notification": rel.notification_preferences.immediate_notification,
                                    "digest_frequency": rel.notification_preferences.digest_frequency,
                                },
                            }
                        )

                return jsonify({"success": True, "relationships": relationships})

            except Exception as e:
                self.logger.error(f"Error getting repository user relationships: {e}")
                return jsonify({"success": False, "message": str(e)}), 500

        @self.app.route(
            "/api/users/<user_id>/repositories/<repo_id>/notification-preferences",
            methods=["GET", "POST"],
        )
        def user_repository_notification_preferences(user_id, repo_id):
            """Get or update user's notification preferences for a specific repository"""
            try:
                # Get the repository
                repo = self.monitor_service.repo_db.get_repository_by_id(repo_id)
                if not repo:
                    return jsonify(
                        {"success": False, "message": "Repository not found"}
                    ), 404

                # Get the user
                user = self.user_service.user_db.get_user_by_id(user_id)
                if not user:
                    return jsonify({"success": False, "message": "User not found"}), 404

                # Find the user's relationship with this repository
                relationship = repo.get_user_relationship(user_id)
                if not relationship:
                    return jsonify(
                        {
                            "success": False,
                            "message": "No relationship found between user and repository",
                        }
                    ), 404

                if request.method == "GET":
                    # Return current notification preferences
                    return jsonify(
                        {
                            "success": True,
                            "notification_preferences": {
                                "enabled_categories": [
                                    cat.value
                                    for cat in relationship.notification_preferences.enabled_categories
                                ],
                                "notify_on_own_commits": relationship.notification_preferences.notify_on_own_commits,
                                "notify_on_high_risk_only": relationship.notification_preferences.notify_on_high_risk_only,
                                "notify_on_specific_paths": relationship.notification_preferences.notify_on_specific_paths,
                                "immediate_notification": relationship.notification_preferences.immediate_notification,
                                "digest_frequency": relationship.notification_preferences.digest_frequency,
                            },
                        }
                    )

                elif request.method == "POST":
                    # Update notification preferences
                    data = request.get_json()
                    prefs_data = data.get("notification_preferences", {})

                    # Update enabled categories
                    from models import NotificationCategory

                    enabled_categories = []
                    for category_name in prefs_data.get("enabled_categories", []):
                        try:
                            enabled_categories.append(
                                NotificationCategory(category_name)
                            )
                        except ValueError:
                            continue

                    relationship.notification_preferences.enabled_categories = (
                        enabled_categories
                    )
                    relationship.notification_preferences.notify_on_own_commits = (
                        prefs_data.get("notify_on_own_commits", False)
                    )
                    relationship.notification_preferences.notify_on_high_risk_only = (
                        prefs_data.get("notify_on_high_risk_only", False)
                    )
                    relationship.notification_preferences.notify_on_specific_paths = (
                        prefs_data.get("notify_on_specific_paths", [])
                    )
                    relationship.notification_preferences.immediate_notification = (
                        prefs_data.get("immediate_notification", True)
                    )
                    relationship.notification_preferences.digest_frequency = (
                        prefs_data.get("digest_frequency", "never")
                    )

                    # Save the repository with updated relationship
                    success = self.monitor_service.repo_db.update_repository(repo)
                    if not success:
                        return jsonify(
                            {
                                "success": False,
                                "message": "Failed to save notification preferences",
                            }
                        ), 500

                    return jsonify(
                        {"success": True, "message": "Notification preferences updated"}
                    )

            except Exception as e:
                self.logger.error(
                    f"Error handling repository notification preferences: {e}"
                )
                return jsonify({"success": False, "message": str(e)}), 500

        # Repository Discovery Routes
        @self.app.route("/repositories/discover")
        def repository_discovery_page():
            return render_template(
                "repository_discovery.html", config=self.monitor_service.config
            )

        @self.app.route("/repositories/discover/scan", methods=["POST"])
        def scan_repositories():
            try:
                base_url = request.form.get("base_url", "").strip()
                username = request.form.get("username", "").strip() or None
                password = request.form.get("password", "").strip() or None
                cache_duration = int(
                    request.form.get("cache_duration", 900)
                )  # Default 15 minutes
                backend_type = request.form.get("backend_type", "svn").strip()
                parent_only = request.form.get("parent_only", "false").lower() == "true"

                # Always show both repository roots AND individual branches (checkbox removed)
                include_branches = True

                if not base_url:
                    return jsonify(
                        {"success": False, "message": "Base URL is required"}
                    )

                discovered_repos = self.backend_manager.discover_repositories(
                    backend_type,
                    base_url,
                    self.monitor_service.config,
                    username,
                    password,
                    cache_duration,
                    include_branches,
                    parent_only,
                )

                # Convert RepositoryInfo objects to dictionaries for JSON response
                repo_dicts = []
                for repo_info in discovered_repos:
                    repo_dict = {
                        "name": repo_info.name,
                        "url": repo_info.url,
                        "path": repo_info.path,
                        "last_revision": repo_info.last_revision,
                        "last_author": repo_info.last_author,
                        "last_date": repo_info.last_date,
                        "size": repo_info.size,
                        "repository_type": repo_info.repository_type,
                        # Add new repository type information
                        "repo_type": getattr(
                            repo_info, "repo_type", "root"
                        ),  # 'root' or 'branch'
                        "branch_type": getattr(
                            repo_info, "branch_type", "trunk"
                        ),  # 'trunk', 'branch', 'tag', 'other'
                    }
                    repo_dicts.append(repo_dict)

                return jsonify({"success": True, "repositories": repo_dicts})

            except Exception as e:
                return jsonify({"success": False, "message": str(e)})

        @self.app.route("/repositories/import", methods=["POST"])
        def import_repository():
            try:
                repo_data = request.json
                if repo_data is None:
                    return jsonify({"success": False, "message": "Invalid JSON data"})

                username = repo_data.get("username")
                password = repo_data.get("password")
                product_doc_files = repo_data.get("product_documentation_files", [])
                risk_aggressiveness = repo_data.get("risk_aggressiveness", "BALANCED")
                risk_description = repo_data.get("risk_description", "")

                # Create repository config from discovered data
                repo_config = RepositoryConfig(
                    name=repo_data["name"],
                    url=repo_data["url"],
                    username=username,
                    password=password,
                    last_revision=int(repo_data.get("last_revision", 0))
                    if repo_data.get("last_revision")
                    else 0,
                    enabled=True,
                    product_documentation_files=product_doc_files,
                    risk_aggressiveness=risk_aggressiveness,
                    risk_description=risk_description,
                )

                # Add to database
                success = self.monitor_service.repo_db.create_repository(repo_config)
                if not success:
                    raise Exception("Failed to save repository to database")

                return jsonify(
                    {
                        "success": True,
                        "message": f"Repository '{repo_config.name}' imported successfully",
                        "repository_id": repo_config.id,
                    }
                )

            except Exception as e:
                return jsonify({"success": False, "message": str(e)})

        @self.app.route("/api/repositories/browse")
        def browse_repository_by_url():
            """Browse repository files by URL for import process"""
            try:
                url = request.args.get("url", "").strip()
                username = request.args.get("username", "").strip() or None
                password = request.args.get("password", "").strip() or None
                path = request.args.get("path", "/").strip()
                filter_docs = request.args.get("filter_docs", "true").lower() == "true"

                if not url:
                    return jsonify({"error": "Repository URL is required"}), 400

                # Determine backend type from URL
                backend_type = "svn"  # Default to SVN for now
                if "git" in url.lower() or url.endswith(".git"):
                    backend_type = "git"

                # Get the appropriate backend
                backend: Optional[RepositoryBackend] = self.backend_manager.get_backend(
                    backend_type, self.monitor_service.config
                )
                if not backend:
                    return jsonify(
                        {"error": f"Unsupported repository type: {backend_type}"}
                    ), 400

                # Browse the repository at the specified path
                self.logger.info(
                    f"Browsing repository: {url}, path: {path}, username: {username}"
                )
                files = backend.browse_files(url, username, password, path)
                self.logger.info(f"Backend returned {len(files)} files")

                # Format the results for the frontend
                formatted_files = []
                for file_info in files:
                    self.logger.debug(f"File: {file_info}")
                    # Apply filtering based on filter_docs parameter
                    if filter_docs:
                        # Only include files that might be documentation or directories
                        if file_info[
                            "type"
                        ] == "directory" or self._is_potential_documentation_file(
                            file_info["name"]
                        ):
                            formatted_files.append(
                                {
                                    "name": file_info["name"],
                                    "type": file_info["type"],
                                    "path": file_info.get(
                                        "path",
                                        f"{path.rstrip('/')}/{file_info['name']}",
                                    ),
                                    "size": file_info.get("size"),
                                    "modified": file_info.get("modified"),
                                }
                            )
                    else:
                        # Include all files and directories
                        formatted_files.append(
                            {
                                "name": file_info["name"],
                                "type": file_info["type"],
                                "path": file_info.get(
                                    "path", f"{path.rstrip('/')}/{file_info['name']}"
                                ),
                                "size": file_info.get("size"),
                                "modified": file_info.get("modified"),
                            }
                        )

                return jsonify(
                    {
                        "success": True,
                        "files": formatted_files,
                        "path": path,
                        "debug": {
                            "backend_type": backend_type,
                            "raw_files_count": len(files),
                            "formatted_files_count": len(formatted_files),
                        },
                    }
                )

            except Exception as e:
                self.logger.error(f"Error browsing repository: {e}")
                # Return more detailed error information
                error_msg = str(e)
                if (
                    "Authentication failed" in error_msg
                    or "No more credentials" in error_msg
                ):
                    error_msg = "Authentication failed. Please check your username and password."
                elif "Unable to connect" in error_msg:
                    error_msg = "Unable to connect to repository. Please check the URL and network connectivity."
                return jsonify({"error": error_msg, "details": str(e)}), 500

        # Document Management Routes
        @self.app.route("/documents")
        def documents_page():
            """Display all generated documents with enhanced filtering, sorting, and organization"""
            # Get query parameters
            page = request.args.get("page", 1, type=int)
            per_page = request.args.get("per_page", 25, type=int)
            repository_id = request.args.get("repository", None)
            code_review_filter = request.args.get("code_review", None)
            doc_impact_filter = request.args.get("doc_impact", None)

            # New enhanced filtering parameters
            risk_level_filter = request.args.get("risk_level", None)
            author_filter = request.args.get("author", None)
            date_from = request.args.get("date_from", None)
            date_to = request.args.get("date_to", None)
            processed_date_from = request.args.get("processed_date_from", None)
            processed_date_to = request.args.get("processed_date_to", None)
            search_query = request.args.get("search", None)

            # Sorting parameters
            sort_by = request.args.get(
                "sort_by", "date"
            )  # date, repository, author, revision, size
            sort_order = request.args.get("sort_order", "desc")  # asc, desc

            # View mode parameters
            view_mode = request.args.get(
                "view_mode", "table"
            )  # table, cards, repository_groups

            # Convert empty strings to None for proper database filtering
            def clean_param(param):
                return None if param == "" else param

            repository_id = clean_param(repository_id)
            author_filter = clean_param(author_filter)
            date_from = clean_param(date_from)
            date_to = clean_param(date_to)
            processed_date_from = clean_param(processed_date_from)
            processed_date_to = clean_param(processed_date_to)
            search_query = clean_param(search_query)
            risk_level_filter = clean_param(risk_level_filter)

            # Convert string filters to boolean
            if code_review_filter == "true":
                code_review_filter = True
            elif code_review_filter == "false":
                code_review_filter = False
            else:
                code_review_filter = None

            if doc_impact_filter == "true":
                doc_impact_filter = True
            elif doc_impact_filter == "false":
                doc_impact_filter = False
            else:
                doc_impact_filter = None

            # Calculate offset
            offset = (page - 1) * per_page

            # Get documents with enhanced filtering and sorting
            documents = self.document_service.get_documents(
                limit=per_page,
                offset=offset,
                repository_id=repository_id,
                code_review_filter=code_review_filter,
                doc_impact_filter=doc_impact_filter,
                risk_level_filter=risk_level_filter,
                author_filter=author_filter,
                date_from=date_from,
                date_to=date_to,
                processed_date_from=processed_date_from,
                processed_date_to=processed_date_to,
                search_query=search_query,
                sort_by=sort_by,
                sort_order=sort_order,
            )

            # Convert to legacy Document objects for template compatibility
            legacy_documents = [Document.from_record(doc) for doc in documents]

            # Get total count for pagination with enhanced filtering
            total_count = self.document_service.get_document_count(
                repository_id=repository_id,
                code_review_filter=code_review_filter,
                doc_impact_filter=doc_impact_filter,
                risk_level_filter=risk_level_filter,
                author_filter=author_filter,
                date_from=date_from,
                date_to=date_to,
                processed_date_from=processed_date_from,
                processed_date_to=processed_date_to,
                search_query=search_query,
            )
            total_pages = (total_count + per_page - 1) // per_page

            # Calculate total size of all documents (not just current page)
            all_documents = self.document_service.get_documents(
                limit=10000
            )  # Get all documents
            total_size = sum(doc.size for doc in all_documents)

            # Get stats and processing info
            stats = self.document_service.get_repository_stats()
            processing_stats = self.document_service.get_processing_stats()

            # Get filter data for dropdowns
            available_repositories = self.document_service.get_available_repositories()
            available_authors = self.document_service.get_available_authors(
                repository_id
            )

            return render_template(
                "documents.html",
                documents=legacy_documents,
                stats=stats,
                processing_stats=processing_stats,
                page=page,
                per_page=per_page,
                total_pages=total_pages,
                total_count=total_count,
                total_size=total_size,
                # Current filters
                repository_filter=repository_id,
                code_review_filter=code_review_filter,
                doc_impact_filter=doc_impact_filter,
                risk_level_filter=risk_level_filter,
                author_filter=author_filter,
                date_from=date_from,
                date_to=date_to,
                processed_date_from=processed_date_from,
                processed_date_to=processed_date_to,
                search_query=search_query,
                sort_by=sort_by,
                sort_order=sort_order,
                view_mode=view_mode,
                # Filter options
                available_repositories=available_repositories,
                available_authors=available_authors,
            )

        @self.app.route("/documents/<path:doc_id>")
        def view_document(doc_id):
            """View a specific document"""
            from diff_service import DiffService

            document = self.document_service.get_document_by_id(doc_id)

            # If document not found, try alternative ID formats (for backward compatibility)
            original_doc_id = doc_id
            if not document:
                # Try adding "_revision_" if it's missing (e.g., "repo_11" -> "repo_revision_11")
                if "_revision_" not in doc_id and "_" in doc_id:
                    parts = doc_id.rsplit("_", 1)
                    if len(parts) == 2 and parts[1].isdigit():
                        alternative_id = f"{parts[0]}_revision_{parts[1]}"
                        self.logger.info(
                            f"Trying alternative document ID format: {alternative_id}"
                        )
                        document = self.document_service.get_document_by_id(
                            alternative_id
                        )
                        if document:
                            self.logger.info(
                                f"Found document with alternative ID: {alternative_id}"
                            )
                            doc_id = alternative_id  # Update doc_id for subsequent operations

                # Try removing "_revision_" if it exists (e.g., "repo_revision_11" -> "repo_11")
                elif "_revision_" in doc_id:
                    alternative_id = doc_id.replace("_revision_", "_")
                    self.logger.info(
                        f"Trying alternative document ID format: {alternative_id}"
                    )
                    document = self.document_service.get_document_by_id(alternative_id)
                    if document:
                        self.logger.info(
                            f"Found document with alternative ID: {alternative_id}"
                        )
                        doc_id = (
                            alternative_id  # Update doc_id for subsequent operations
                        )

            if not document:
                self.logger.warning(f"Document not found in database: {doc_id}")

                # Try to provide helpful suggestions by finding similar documents
                error_message = f'Document not found. The document with ID "{original_doc_id}" may have been deleted or moved.'

                # Extract repository ID and revision from the requested doc_id
                if "_" in original_doc_id:
                    parts = original_doc_id.split("_")
                    if len(parts) >= 2:
                        potential_repo_id = "_".join(parts[:-1])
                        if "revision" in parts:
                            # Remove 'revision' part to get clean repo ID
                            repo_parts = [p for p in parts if p != "revision"]
                            if len(repo_parts) >= 2:
                                potential_repo_id = "_".join(repo_parts[:-1])

                        # Look for other documents from the same repository
                        try:
                            similar_docs = self.document_service.get_documents(
                                repository_id=potential_repo_id, limit=5
                            )
                            if similar_docs:
                                revisions = [str(doc.revision) for doc in similar_docs]
                                error_message += f" Available revisions for this repository: {', '.join(revisions)}."
                        except Exception as e:
                            self.logger.debug(f"Error finding similar documents: {e}")

                # Try to provide recovery information
                recovery_info = None
                if "_" in original_doc_id:
                    try:
                        repo_id, revision_str = original_doc_id.rsplit("_", 1)
                        revision = int(revision_str)

                        # Check if repository exists and revision is available
                        repo_config = self.monitor_service.config.get_repository_by_id(
                            repo_id
                        )
                        if repo_config:
                            backend_manager = get_backend_manager()
                            backend = backend_manager.get_backend_for_repository(
                                repo_config, self.monitor_service.config
                            )
                            if backend:
                                commit_info = backend.get_commit_info(
                                    repo_config, str(revision)
                                )
                                if commit_info:
                                    recovery_info = {
                                        "doc_id": original_doc_id,
                                        "repository_name": repo_config.name,
                                        "revision": revision,
                                        "author": commit_info.author,
                                        "message": commit_info.message,
                                    }
                                    error_message += f" However, revision {revision} exists in the repository and can be recovered."
                    except (ValueError, Exception) as e:
                        self.logger.debug(
                            f"Error checking recovery options for {original_doc_id}: {e}"
                        )

                flash(error_message, "error")

                # If recovery is possible, redirect to documents page with recovery info
                if recovery_info:
                    return redirect(
                        url_for(
                            "documents_page",
                            recovery_doc_id=recovery_info["doc_id"],
                            recovery_repo=recovery_info["repository_name"],
                            recovery_revision=recovery_info["revision"],
                        )
                    )
                else:
                    return redirect(url_for("documents_page"))

            # Check if document is orphaned (repository no longer exists)
            document_record = self.document_service.get_document_record_by_id(doc_id)
            if document_record:
                repositories = self.monitor_service.repo_db.get_all_repositories()
                valid_repo_ids = set(repo.id for repo in repositories)
                valid_repo_names = set(repo.name for repo in repositories)

                # Check by both ID and name to handle repository ID format changes
                is_orphaned = (
                    document_record.repository_id not in valid_repo_ids
                    and document_record.repository_name not in valid_repo_names
                )

                if is_orphaned:
                    self.logger.warning(
                        f"Attempted to view orphaned document: {doc_id} (repo ID: {document_record.repository_id}, repo name: {document_record.repository_name})"
                    )
                    flash(
                        f"Document unavailable. This document belongs to a repository that has been removed from the configuration. Repository: {document_record.repository_name} (ID: {document_record.repository_id})",
                        "warning",
                    )
                    return redirect(url_for("documents_page"))

            content = self.document_service.get_document_content(doc_id)
            if not content:
                # Get document record to check if file exists
                document_record = self.document_service.get_document_record_by_id(
                    doc_id
                )
                if document_record:
                    import os

                    if not os.path.exists(document_record.filepath):
                        self.logger.error(
                            f"Document file missing: {document_record.filepath}"
                        )
                        flash(
                            f'Document file not found. The file "{document_record.filepath}" appears to have been moved or deleted from the filesystem.',
                            "error",
                        )
                    else:
                        self.logger.error(
                            f"Error reading document content: {document_record.filepath}"
                        )
                        flash(
                            "Unable to read document content. The file may be corrupted or have permission issues.",
                            "error",
                        )
                else:
                    flash(
                        "Document content unavailable. Unable to locate document data.",
                        "error",
                    )
                return redirect(url_for("documents_page"))

            # Check if diff should be included
            include_diff = request.args.get("include_diff", "false").lower() == "true"
            diff_format = request.args.get("diff_format", "unified")
            diff_content = None
            raw_diff_content = None

            if include_diff:
                # Get document record for diff generation
                document_record = self.document_service.get_document_record_by_id(
                    doc_id
                )
                if document_record:
                    diff_service = DiffService(self.monitor_service.config_manager)
                    if diff_service.can_generate_diff(document_record):
                        # Get raw diff content first (unified format for copying)
                        raw_diff_content = diff_service.get_diff_for_document(
                            document_record, "unified"
                        )

                        # Get formatted diff content for display
                        if diff_format == "side-by-side":
                            diff_content = diff_service.get_diff_for_document(
                                document_record, "side-by-side"
                            )
                        else:
                            diff_content = raw_diff_content

                        # Add diff to document content if available
                        if diff_content:
                            document.diff = diff_content

            # Don't load AI suggestions synchronously - they will be loaded via AJAX
            # This prevents the page from being sluggish on initial load
            ai_suggestions = None

            html = render_template(
                "document_view.html",
                document=document,
                content=content,
                include_diff=include_diff,
                diff_content=diff_content,
                raw_diff_content=raw_diff_content,
                diff_format=diff_format,
                ai_suggestions=ai_suggestions,
                config=self.monitor_service.config,
            )
            # Add no-store cache headers to avoid stale document content
            response = make_response(html)
            response.headers["Cache-Control"] = (
                "no-store, no-cache, must-revalidate, max-age=0, private"
            )
            response.headers["Pragma"] = "no-cache"
            response.headers["Expires"] = "0"
            return response

        @self.app.route("/api/processing-status")
        def api_processing_status():
            """Get current processing status for user feedback"""
            try:
                stats = self.unified_processor.get_stats()

                # Get current processing tasks (if any)
                current_tasks = []
                if hasattr(self.unified_processor, "_get_current_tasks"):
                    current_tasks = self.unified_processor._get_current_tasks()

                return jsonify(
                    {
                        "success": True,
                        "processing": {
                            "queue_size": stats["queue_size"],
                            "active_threads": stats["active_threads"],
                            "processed_count": stats["processed_count"],
                            "error_count": stats["error_count"],
                            "running": stats["running"],
                            "current_tasks": current_tasks,
                        },
                    }
                )
            except Exception as e:
                self.logger.error(f"Error getting processing status: {e}")
                return jsonify({"success": False, "error": str(e)})

        @self.app.route("/api/documents")
        def api_documents():
            """API endpoint for documents list with pagination"""
            # Get query parameters
            limit = request.args.get("limit", 50, type=int)
            offset = request.args.get("offset", 0, type=int)
            # Support both 'repository' and 'repository_id' parameter names for flexibility
            repository_id = request.args.get("repository_id", None) or request.args.get(
                "repository", None
            )
            code_review_filter = request.args.get("code_review", None)
            doc_impact_filter = request.args.get("doc_impact", None)

            # Convert string filters to boolean
            if code_review_filter == "true":
                code_review_filter = True
            elif code_review_filter == "false":
                code_review_filter = False
            else:
                code_review_filter = None

            if doc_impact_filter == "true":
                doc_impact_filter = True
            elif doc_impact_filter == "false":
                doc_impact_filter = False
            else:
                doc_impact_filter = None

            # Get documents
            documents = self.document_service.get_documents(
                limit=limit,
                offset=offset,
                repository_id=repository_id,
                code_review_filter=code_review_filter,
                doc_impact_filter=doc_impact_filter,
            )

            # Get total count
            total_count = self.document_service.get_document_count(repository_id)

            return jsonify(
                {
                    "documents": [
                        {
                            "id": doc.id,
                            "repository_id": doc.repository_id,
                            "repository_name": doc.repository_name,
                            "revision": doc.revision,
                            "date": doc.date.isoformat(),
                            "author": doc.author,
                            "commit_message": doc.commit_message,
                            "filename": doc.filename,
                            "size": doc.size,
                            "code_review_recommended": doc.code_review_recommended,
                            "code_review_priority": doc.code_review_priority,
                            "documentation_impact": doc.documentation_impact,
                            "risk_level": doc.risk_level,
                            "heuristic_context": doc.heuristic_context,
                        }
                        for doc in documents
                    ],
                    "total_count": total_count,
                    "stats": self.document_service.get_repository_stats(),
                    "processing_stats": self.document_service.get_processing_stats(),
                }
            )

        @self.app.route("/api/documents/<path:doc_id>/delete", methods=["POST"])
        def api_delete_document(doc_id):
            """Delete a document, including orphaned documents"""
            try:
                self.logger.info(f"Attempting to delete document: {doc_id}")

                # First check if document exists
                document = self.document_service.get_document_record_by_id(doc_id)
                self.logger.info(f"Document lookup result: {document}")

                # If document not found, try alternative ID formats (for backward compatibility)
                if not document:
                    original_doc_id = doc_id
                    # Try adding "_revision_" if it's missing
                    if "_revision_" not in doc_id and "_" in doc_id:
                        parts = doc_id.rsplit("_", 1)
                        if len(parts) == 2 and parts[1].isdigit():
                            alternative_id = f"{parts[0]}_revision_{parts[1]}"
                            self.logger.info(
                                f"Trying alternative document ID format: {alternative_id}"
                            )
                            document = self.document_service.get_document_record_by_id(
                                alternative_id
                            )
                            if document:
                                doc_id = alternative_id  # Update doc_id for subsequent operations

                    # Try removing "_revision_" if it exists
                    elif "_revision_" in doc_id:
                        alternative_id = doc_id.replace("_revision_", "_")
                        self.logger.info(
                            f"Trying alternative document ID format: {alternative_id}"
                        )
                        document = self.document_service.get_document_record_by_id(
                            alternative_id
                        )
                        if document:
                            doc_id = alternative_id  # Update doc_id for subsequent operations

                if not document:
                    self.logger.warning(f"Document not found in database: {doc_id}")
                    return jsonify(
                        {
                            "success": False,
                            "message": f'Document not found. The document with ID "{doc_id}" may have been deleted or is no longer available.',
                            "error_code": "DOCUMENT_NOT_FOUND",
                        }
                    ), 404

                # Check if document is orphaned (repository no longer exists)
                config = self.config_manager.load_config()
                valid_repo_ids = set(repo.id for repo in config.repositories)
                is_orphaned = document.repository_id not in valid_repo_ids

                if is_orphaned:
                    # For orphaned documents, just remove from database
                    success = self.document_service.delete_document(doc_id)
                    if success:
                        self.logger.info(
                            f"Deleted orphaned document: {doc_id} (repo: {document.repository_id})"
                        )
                        return jsonify(
                            {
                                "success": True,
                                "message": "Orphaned document removed from database",
                            }
                        )
                    else:
                        return jsonify(
                            {
                                "success": False,
                                "message": "Failed to delete orphaned document",
                            }
                        ), 500
                else:
                    # For valid documents, use normal deletion process
                    success = self.document_service.delete_document(doc_id)
                    if success:
                        return jsonify(
                            {
                                "success": True,
                                "message": "Document deleted successfully",
                            }
                        )
                    else:
                        return jsonify(
                            {"success": False, "message": "Failed to delete document"}
                        ), 500

            except Exception as e:
                self.logger.error(f"Error deleting document {doc_id}: {e}")
                return jsonify(
                    {"success": False, "message": f"Error deleting document: {str(e)}"}
                ), 500

        @self.app.route("/api/documents/cleanup-orphaned", methods=["POST"])
        def api_cleanup_orphaned_documents():
            """Remove all orphaned documents from database"""
            try:
                # Get valid repository IDs
                config = self.config_manager.load_config()
                valid_repo_ids = set(repo.id for repo in config.repositories)

                # Get all documents
                all_documents = self.document_service.get_documents()

                orphaned_count = 0
                for doc in all_documents:
                    if doc.repository_id not in valid_repo_ids:
                        success = self.document_service.delete_document(doc.id)
                        if success:
                            orphaned_count += 1
                            self.logger.info(
                                f"Cleaned up orphaned document: {doc.id} (repo: {doc.repository_id})"
                            )
                        else:
                            self.logger.warning(
                                f"Failed to delete orphaned document: {doc.id}"
                            )

                return jsonify(
                    {
                        "success": True,
                        "message": f"Cleaned up {orphaned_count} orphaned documents",
                        "orphaned_count": orphaned_count,
                    }
                )
            except Exception as e:
                self.logger.error(f"Error cleaning up orphaned documents: {e}")
                return jsonify({"success": False, "message": str(e)}), 500

        @self.app.route("/api/documents/rescan", methods=["POST"])
        def api_rescan_documents():
            """Force rescan of all documents with enhanced feedback"""
            try:
                # Get current stats before rescan
                stats_before = self.unified_processor.get_stats()

                # Trigger rescan
                self.document_service.force_rescan()

                # Get stats after rescan to see how many documents were queued
                stats_after = self.unified_processor.get_stats()

                queued_count = stats_after["queue_size"] - stats_before["queue_size"]

                return jsonify(
                    {
                        "success": True,
                        "message": "Document rescan initiated",
                        "queued_count": max(0, queued_count),
                        "processing_info": {
                            "queue_size": stats_after["queue_size"],
                            "active_threads": stats_after["active_threads"],
                        },
                    }
                )
            except Exception as e:
                return jsonify({"success": False, "message": str(e)})

        @self.app.route("/api/documents/<path:doc_id>/rescan", methods=["POST"])
        def api_rescan_document(doc_id):
            """Rescan a document with a specific AI model"""
            try:
                # URL decode the document ID to handle slashes
                from urllib.parse import unquote

                doc_id = unquote(doc_id)
                self.logger.info(f"Attempting to rescan document: {doc_id}")

                # Get request data
                try:
                    data = request.get_json() or {}
                except Exception as json_error:
                    self.logger.error(
                        f"Failed to parse JSON request data: {json_error}"
                    )
                    return jsonify(
                        {
                            "success": False,
                            "message": f"Invalid JSON in request: {str(json_error)}",
                        }
                    ), 400

                model = data.get("model", "").strip()
                aggressiveness = data.get("aggressiveness", "BALANCED").strip()
                preserve_user_feedback = data.get("preserve_user_feedback", True)

                self.logger.info(
                    f"Rescan request - Model: {model}, Aggressiveness: {aggressiveness}, Preserve feedback: {preserve_user_feedback}"
                )

                if not model:
                    return jsonify(
                        {"success": False, "message": "AI model is required"}
                    ), 400

                # Validate aggressiveness level
                valid_aggressiveness = [
                    "CONSERVATIVE",
                    "BALANCED",
                    "AGGRESSIVE",
                    "VERY_AGGRESSIVE",
                ]
                if aggressiveness not in valid_aggressiveness:
                    return jsonify(
                        {
                            "success": False,
                            "message": f"Invalid aggressiveness level. Must be one of: {', '.join(valid_aggressiveness)}",
                        }
                    ), 400

                # Get the document - with recovery for missing documents
                document = self.document_service.get_document_by_id(doc_id)
                self.logger.info(
                    f"🔍 DEBUG: Document lookup result for '{doc_id}': {document}"
                )
                if document:
                    self.logger.info(
                        f"🔍 DEBUG: Document repository_id: '{document.repository_id}', repository_name: '{document.repository_name}'"
                    )
                if not document:
                    # Document not found in database - try to recover from document ID
                    self.logger.warning(
                        f"Document {doc_id} not found in database, attempting recovery"
                    )

                    # Parse document ID to extract repository ID and revision
                    try:
                        if "_" in doc_id:
                            repo_id, revision = doc_id.rsplit("_", 1)
                            revision = int(revision)

                            # Get repository config
                            repo_config = (
                                self.monitor_service.repo_db.get_repository_by_id(
                                    repo_id
                                )
                            )
                            if not repo_config:
                                return jsonify(
                                    {
                                        "success": False,
                                        "message": f"Document not found and repository {repo_id} not configured",
                                    }
                                ), 404

                            # Try to get commit info from repository
                            backend_manager = get_backend_manager()
                            backend = backend_manager.get_backend_for_repository(
                                repo_config, self.monitor_service.config
                            )

                            if backend:
                                commit_info = backend.get_commit_info(
                                    repo_config, str(revision)
                                )
                                if commit_info:
                                    # We can recover this document - offer to recreate it
                                    return jsonify(
                                        {
                                            "success": False,
                                            "message": "Document not found in database but exists in repository",
                                            "recovery_available": True,
                                            "repository_name": repo_config.name,
                                            "revision": revision,
                                            "author": commit_info.author,
                                            "commit_message": commit_info.message,
                                            "suggestion": f"Use Historical Scan to recreate this document, or scan revision {revision} specifically",
                                        }
                                    ), 404

                            return jsonify(
                                {
                                    "success": False,
                                    "message": f"Document not found and cannot access repository data for revision {revision}",
                                }
                            ), 404
                        else:
                            return jsonify(
                                {
                                    "success": False,
                                    "message": "Document not found and document ID format is invalid",
                                }
                            ), 404

                    except (ValueError, Exception) as e:
                        self.logger.error(
                            f"Error attempting document recovery for {doc_id}: {e}"
                        )
                        return jsonify(
                            {
                                "success": False,
                                "message": f"Document not found and recovery failed: {str(e)}",
                            }
                        ), 404

                # Validate that the model is available
                available_models = self.monitor_service.get_available_models()
                if model not in available_models:
                    return jsonify(
                        {
                            "success": False,
                            "message": f'Model "{model}" is not available. Available models: {", ".join(available_models)}',
                        }
                    ), 400

                # Store user feedback if preserving
                user_feedback_data = {}
                if preserve_user_feedback:
                    # Collect all user feedback fields
                    user_feedback_data = {
                        "user_code_review_status": document.user_code_review_status,
                        "user_code_review_comments": document.user_code_review_comments,
                        "user_code_review_reviewer": document.user_code_review_reviewer,
                        "user_code_review_date": document.user_code_review_date,
                        "user_documentation_rating": document.user_documentation_rating,
                        "user_documentation_comments": document.user_documentation_comments,
                        "user_documentation_updated_by": document.user_documentation_updated_by,
                        "user_documentation_updated_date": document.user_documentation_updated_date,
                        "user_risk_assessment_override": document.user_risk_assessment_override,
                        "user_risk_assessment_comments": document.user_risk_assessment_comments,
                        "user_risk_assessment_updated_by": document.user_risk_assessment_updated_by,
                        "user_risk_assessment_updated_date": document.user_risk_assessment_updated_date,
                        "user_documentation_input": document.user_documentation_input,
                        "user_documentation_suggestions": document.user_documentation_suggestions,
                        "user_documentation_input_by": document.user_documentation_input_by,
                        "user_documentation_input_date": document.user_documentation_input_date,
                    }

                # Get repository config first - try by ID first, then by name as fallback
                self.logger.info(
                    f"🔍 DEBUG: Getting repository config for ID: {document.repository_id}"
                )
                repo_config = self.monitor_service.repo_db.get_repository_by_id(
                    document.repository_id
                )
                if not repo_config:
                    self.logger.info(
                        f"🔍 DEBUG: Repository ID lookup failed, trying by name: {document.repository_name}"
                    )
                    repo_config = self.monitor_service.repo_db.get_repository_by_name(
                        document.repository_name
                    )
                if not repo_config:
                    self.logger.error(
                        f"🔍 DEBUG: Repository configuration not found for ID: {document.repository_id} or name: {document.repository_name}"
                    )
                    return jsonify(
                        {
                            "success": False,
                            "message": f"Repository configuration not found for {document.repository_name}",
                        }
                    ), 404
                self.logger.info(
                    f"🔍 DEBUG: Found repository config: {repo_config.name}"
                )

                # Get fresh commit info from SVN (not cached data) to ensure complete rescan
                self.logger.info(
                    f"Fetching fresh commit data from SVN for revision {document.revision}"
                )
                self.logger.info(
                    f"🔍 DEBUG: Getting backend for repository: {repo_config.name}"
                )
                backend = (
                    self.monitor_service.backend_manager.get_backend_for_repository(
                        repo_config, self.monitor_service.config
                    )
                )
                if not backend:
                    self.logger.error(
                        f"🔍 DEBUG: No backend available for repository: {repo_config.name}"
                    )
                    return jsonify(
                        {
                            "success": False,
                            "message": "No backend available for repository",
                        }
                    ), 500
                self.logger.info(f"🔍 DEBUG: Got backend: {type(backend).__name__}")

                # Get fresh commit info with current diff data from repository
                self.logger.info(
                    f"🔍 DEBUG: Calling backend.get_commit_info for revision {document.revision}"
                )
                commit_info = backend.get_commit_info(
                    repo_config, str(document.revision)
                )
                if not commit_info:
                    self.logger.warning(
                        f"Could not get fresh commit info from SVN for revision {document.revision}, falling back to cached data"
                    )
                    self.logger.info(
                        f"🔍 DEBUG: Creating fallback commit_info from cached data"
                    )
                    # Fallback to cached data if SVN fetch fails
                    from models import CommitInfo

                    commit_info = CommitInfo(
                        repository_id=document.repository_id,
                        repository_name=document.repository_name,
                        revision=document.revision,
                        author=document.author,
                        date=document.date.isoformat() if document.date else "",
                        message=document.commit_message,
                        changed_paths=document.changed_paths or [],
                        diff=getattr(document, "diff", "") or "",
                    )
                    self.logger.info(
                        f"🔍 DEBUG: Fallback commit_info created with {len(commit_info.changed_paths)} changed paths"
                    )
                else:
                    self.logger.info(
                        f"Successfully fetched fresh commit data from SVN for revision {document.revision}"
                    )
                    self.logger.info(
                        f"🔍 DEBUG: Fresh commit_info has {len(commit_info.changed_paths)} changed paths"
                    )
                    # Ensure repository_id is set correctly for fresh commit info
                    commit_info.repository_id = document.repository_id

                # Reprocess with the specified model and aggressiveness using unified processor
                self.logger.info(
                    f"Rescanning document {doc_id} with model: {model}, aggressiveness: {aggressiveness}"
                )
                self.logger.info(f"🔍 DEBUG: About to set model overrides")

                # Temporarily override the model and aggressiveness in config for this processing
                original_model = self.monitor_service.config.ollama_model
                original_aggressiveness = repo_config.risk_aggressiveness
                self.logger.info(
                    f"🔍 DEBUG: Original model: {original_model}, Original aggressiveness: {original_aggressiveness}"
                )

                self.monitor_service.config.ollama_model = model
                repo_config.risk_aggressiveness = aggressiveness
                self.logger.info(
                    f"🔍 DEBUG: Set model override to: {model}, aggressiveness to: {aggressiveness}"
                )

                try:
                    # Generate documentation with the specified model first
                    self.logger.info(
                        f"🔍 DEBUG: About to call generate_documentation with model override: {model}"
                    )
                    self.logger.info(
                        f"Generating documentation for revision {document.revision} with model {model}"
                    )
                    documentation = (
                        self.monitor_service.ollama_client.generate_documentation(
                            commit_info, model_override=model
                        )
                    )
                    self.logger.info(
                        f"🔍 DEBUG: generate_documentation returned {len(documentation) if documentation else 0} characters"
                    )

                    if not documentation or documentation.startswith("Error:"):
                        error_msg = (
                            f"Failed to generate documentation with model {model}"
                        )
                        if documentation and documentation.startswith("Error:"):
                            error_msg += f": {documentation}"
                        self.logger.error(error_msg)
                        return jsonify({"success": False, "message": error_msg}), 500

                    self.logger.info(
                        f"Documentation generated successfully ({len(documentation)} characters)"
                    )

                    # Process the commit with the generated documentation and specified model/aggressiveness
                    success = self.unified_processor.process_commit_with_model(
                        commit_info=commit_info,
                        repository_config=repo_config,
                        documentation=documentation,  # Pass the generated documentation
                        priority=10,  # High priority for rescan
                        override_model=model,  # Pass the selected model directly
                        override_aggressiveness=aggressiveness,  # Pass the selected aggressiveness directly
                    )

                    if success:
                        # Wait for processing to complete (since process_commit only queues the task)
                        import time

                        self.logger.info(
                            f"Waiting for rescan processing to complete for document {doc_id}"
                        )

                        # Wait and monitor progress for up to 60 seconds
                        max_wait_time = 60
                        wait_interval = 2
                        waited_time = 0
                        processing_complete = False

                        while waited_time < max_wait_time:
                            time.sleep(wait_interval)
                            waited_time += wait_interval

                            # Check if processing is complete by looking for the document file
                            # repo_config.name now includes branch path (e.g., "reposense_cpp_test/trunk")
                            expected_file = f"/app/data/output/repositories/{repo_config.name}/docs/revision_{document.revision}.md"
                            alt_expected_file = f"/app/data/output/repositories/{document.repository_name}/docs/revision_{document.revision}.md"
                            for check_path in [expected_file, alt_expected_file]:
                                if (
                                    os.path.exists(check_path)
                                    and os.path.getsize(check_path) > 0
                                ):
                                    processing_complete = True
                                    self.logger.info(
                                        f"RESCAN_STEP: Document file created successfully: {check_path} (size={os.path.getsize(check_path)} bytes)"
                                    )
                                    break
                            if processing_complete:
                                break

                            # Also check queue status
                            stats = self.unified_processor.get_stats()
                            if (
                                stats["queue_size"] == 0
                                and stats["active_threads"] == 0
                            ):
                                # Queue is empty and no active processing - check once more for file
                                time.sleep(1)
                                if (
                                    os.path.exists(expected_file)
                                    and os.path.getsize(expected_file) > 0
                                ):
                                    processing_complete = True
                                    break

                        if not processing_complete:
                            self.logger.error(
                                f"Rescan processing did not complete within {max_wait_time} seconds"
                            )
                            return jsonify(
                                {
                                    "success": False,
                                    "message": "Document processing timed out",
                                }
                            ), 500

                        # Log beginning of user feedback restoration
                        self.logger.info(
                            f"RESCAN_STEP: preserve_user_feedback={preserve_user_feedback}"
                        )
                        if preserve_user_feedback and any(user_feedback_data.values()):
                            # Find the newly created document by searching for it
                            # Get documents for this repository and find the one with matching revision
                            repo_documents = self.document_service.get_documents(
                                limit=100, repository_id=document.repository_id
                            )
                            new_document = None
                            for doc in repo_documents:
                                if doc.revision == document.revision:
                                    new_document = doc
                                    break

                            if new_document:
                                # Restore user feedback using database update
                                try:
                                    # Update the document record directly in the database
                                    self.document_service.db.update_user_feedback(
                                        new_document.id, user_feedback_data
                                    )
                                    self.logger.info(
                                        f"Restored user feedback for rescanned document {new_document.id}"
                                    )
                                except Exception as e:
                                    self.logger.warning(
                                        f"Failed to restore user feedback: {e}"
                                    )
                                    # Continue anyway - the rescan was successful

                        self.logger.info(
                            f"Successfully rescanned document {doc_id} with model {model} and {aggressiveness} aggressiveness"
                        )

                        # Emit rescan success notification event
                        if self.monitor_service.notification_manager:
                            try:
                                from notification_system import EventFactory

                                event = EventFactory.create_processing_event(
                                    title=f"Document Rescan Complete: {document.repository_name} rev {document.revision}",
                                    message=f"Successfully rescanned document {doc_id} with {model} model and {aggressiveness.lower()} risk assessment",
                                    repository_id=document.repository_id,
                                    success=True,
                                )
                                event.metadata.update(
                                    {
                                        "source": "web_rescan",
                                        "document_id": doc_id,
                                        "ai_model": model,
                                        "aggressiveness": aggressiveness,
                                        "preserve_feedback": preserve_user_feedback,
                                    }
                                )
                                self.monitor_service.notification_manager.emit_event(
                                    event
                                )
                            except Exception as e:
                                self.logger.error(
                                    f"Error emitting rescan success event: {e}"
                                )

                        return jsonify(
                            {
                                "success": True,
                                "message": f"Document rescanned successfully with {model} ({aggressiveness.lower()} risk assessment)",
                                "refresh_needed": True,  # Signal client to refresh
                            }
                        )
                    else:
                        # Emit rescan failure notification event
                        if self.monitor_service.notification_manager:
                            try:
                                from notification_system import EventFactory

                                event = EventFactory.create_processing_event(
                                    title=f"Document Rescan Failed: {document.repository_name} rev {document.revision}",
                                    message=f"Failed to queue document {doc_id} for reprocessing with {model} model",
                                    repository_id=document.repository_id,
                                    success=False,
                                )
                                event.metadata.update(
                                    {
                                        "source": "web_rescan",
                                        "document_id": doc_id,
                                        "ai_model": model,
                                        "aggressiveness": aggressiveness,
                                        "error": "Failed to queue for reprocessing",
                                    }
                                )
                                self.monitor_service.notification_manager.emit_event(
                                    event
                                )
                            except Exception as e:
                                self.logger.error(
                                    f"Error emitting rescan failure event: {e}"
                                )

                        return jsonify(
                            {
                                "success": False,
                                "message": "Failed to queue document for reprocessing",
                            }
                        ), 500

                finally:
                    # Restore original settings
                    self.monitor_service.config.ollama_model = original_model
                    repo_config.risk_aggressiveness = original_aggressiveness

            except Exception as e:
                self.logger.error(f"Error rescanning document {doc_id}: {e}")
                import traceback

                traceback.print_exc()
                return jsonify({"success": False, "message": str(e)}), 500

        @self.app.route("/api/documents/<path:doc_id>/recover", methods=["POST"])
        def api_recover_document(doc_id):
            """Recover a missing document from repository data"""
            try:
                # URL decode the document ID to handle slashes
                from urllib.parse import unquote

                doc_id = unquote(doc_id)
                self.logger.info(f"Attempting to recover document: {doc_id}")

                # Parse document ID to extract repository ID and revision
                if "_" not in doc_id:
                    return jsonify(
                        {"success": False, "message": "Invalid document ID format"}
                    ), 400

                repo_id, revision_str = doc_id.rsplit("_", 1)
                try:
                    revision = int(revision_str)
                except ValueError:
                    return jsonify(
                        {
                            "success": False,
                            "message": "Invalid revision number in document ID",
                        }
                    ), 400

                # Get repository config
                repo_config = self.monitor_service.repo_db.get_repository_by_id(repo_id)
                if not repo_config:
                    return jsonify(
                        {
                            "success": False,
                            "message": f"Repository {repo_id} not found in configuration",
                        }
                    ), 404

                # Get commit info from repository
                backend_manager = get_backend_manager()
                backend = backend_manager.get_backend_for_repository(
                    repo_config, self.monitor_service.config
                )

                if not backend:
                    return jsonify(
                        {
                            "success": False,
                            "message": f"No backend available for repository type: {repo_config.type}",
                        }
                    ), 500

                commit_info = backend.get_commit_info(repo_config, str(revision))
                if not commit_info:
                    return jsonify(
                        {
                            "success": False,
                            "message": f"Revision {revision} not found in repository",
                        }
                    ), 404

                # Update repository aggressiveness if provided
                data = request.get_json() or {}
                aggressiveness = data.get("aggressiveness", "BALANCED").strip()
                valid_aggressiveness = [
                    "CONSERVATIVE",
                    "BALANCED",
                    "AGGRESSIVE",
                    "VERY_AGGRESSIVE",
                ]
                if aggressiveness in valid_aggressiveness:
                    original_aggressiveness = repo_config.risk_aggressiveness
                    repo_config.risk_aggressiveness = aggressiveness
                else:
                    original_aggressiveness = repo_config.risk_aggressiveness

                try:
                    # Generate documentation first
                    self.logger.info(
                        f"Generating documentation for recovery of revision {revision}"
                    )
                    documentation = (
                        self.monitor_service.ollama_client.generate_documentation(
                            commit_info
                        )
                    )

                    if not documentation or documentation.startswith("Error:"):
                        error_msg = f"Failed to generate documentation for recovery"
                        if documentation and documentation.startswith("Error:"):
                            error_msg += f": {documentation}"
                        self.logger.error(error_msg)
                        return jsonify({"success": False, "message": error_msg}), 500

                    self.logger.info(
                        f"Documentation generated successfully for recovery ({len(documentation)} characters)"
                    )

                    # Process the commit to recreate the document
                    success = self.unified_processor.process_commit(
                        commit_info=commit_info,
                        repository_config=repo_config,
                        documentation=documentation,  # Pass the generated documentation
                        priority=10,  # High priority for recovery
                    )

                    if success:
                        # Wait for processing to complete
                        import time

                        self.logger.info(
                            f"Waiting for document recovery to complete for {doc_id}"
                        )

                        max_wait_time = 60
                        wait_interval = 2
                        waited_time = 0
                        processing_complete = False

                        while waited_time < max_wait_time:
                            time.sleep(wait_interval)
                            waited_time += wait_interval

                            # Check if document was created
                            recovered_document = (
                                self.document_service.get_document_by_id(doc_id)
                            )
                            if recovered_document:
                                processing_complete = True
                                self.logger.info(
                                    f"Document recovered successfully: {doc_id}"
                                )
                                break

                            # Also check queue status
                            stats = self.unified_processor.get_stats()
                            if (
                                stats["queue_size"] == 0
                                and stats["active_threads"] == 0
                            ):
                                time.sleep(1)  # Final check
                                recovered_document = (
                                    self.document_service.get_document_by_id(doc_id)
                                )
                                if recovered_document:
                                    processing_complete = True
                                    break

                        if processing_complete:
                            return jsonify(
                                {
                                    "success": True,
                                    "message": f"Document recovered successfully from repository revision {revision}",
                                    "document_id": doc_id,
                                }
                            )
                        else:
                            return jsonify(
                                {
                                    "success": False,
                                    "message": "Document recovery timed out - processing may still be in progress",
                                }
                            ), 500
                    else:
                        return jsonify(
                            {
                                "success": False,
                                "message": "Failed to queue document recovery",
                            }
                        ), 500

                finally:
                    # Restore original aggressiveness
                    repo_config.risk_aggressiveness = original_aggressiveness

            except Exception as e:
                self.logger.error(f"Error recovering document {doc_id}: {e}")
                return jsonify({"success": False, "message": str(e)}), 500

        @self.app.route("/api/documents/clear-cache", methods=["POST"])
        def api_clear_document_cache():
            """Clear document cache"""
            try:
                self.document_service._invalidate_cache()
                return jsonify({"success": True, "message": "Document cache cleared"})
            except Exception as e:
                return jsonify({"success": False, "message": str(e)})

        @self.app.route("/api/documents/delete-filtered", methods=["POST"])
        def api_delete_filtered_documents():
            """Delete documents matching current filters"""
            try:
                # Get filter parameters from request
                filters = request.get_json() or {}

                # Convert filter parameters to match document service expectations
                filter_params = {
                    "repository_id": filters.get("repository_id") or None,
                    "search_query": filters.get("search_query") or None,
                    "author_filter": filters.get("author_filter") or None,
                    "date_from": filters.get("date_from") or None,
                    "date_to": filters.get("date_to") or None,
                    "code_review_filter": _parse_bool_filter(
                        filters.get("code_review_filter")
                    ),
                    "doc_impact_filter": _parse_bool_filter(
                        filters.get("doc_impact_filter")
                    ),
                    "risk_level_filter": filters.get("risk_level_filter") or None,
                }

                # Remove None values
                filter_params = {
                    k: v for k, v in filter_params.items() if v is not None
                }

                # Delete filtered documents
                deleted_count, total_count = (
                    self.document_service.delete_filtered_documents(**filter_params)
                )

                # Create appropriate message
                if filter_params:
                    message = f"Deleted {deleted_count} filtered documents (out of {total_count} matching documents)"
                else:
                    message = f"Deleted all {deleted_count} documents in the system"

                return jsonify(
                    {
                        "success": True,
                        "message": message,
                        "deleted_count": deleted_count,
                        "total_count": total_count,
                        "had_filters": bool(filter_params),
                    }
                )
            except Exception as e:
                self.logger.error(f"Error deleting filtered documents: {e}")
                return jsonify({"success": False, "message": str(e)}), 500

        @self.app.route("/api/documents/delete-all", methods=["POST"])
        def api_delete_all_documents():
            """Delete ALL documents (database records and physical files) - Legacy endpoint"""
            try:
                import os
                import shutil

                # Count existing files first
                repositories_dir = "/app/data/output/repositories"
                file_count = 0
                if os.path.exists(repositories_dir):
                    for root, dirs, files in os.walk(repositories_dir):
                        file_count += len([f for f in files if f.endswith(".md")])

                # Delete the entire repositories directory
                if os.path.exists(repositories_dir):
                    shutil.rmtree(repositories_dir)
                    self.logger.info(
                        f"Deleted repositories directory: {repositories_dir}"
                    )

                # Clear all documents from database
                db_count = self.document_service.clear_all_documents()

                # Clear document cache
                self.document_service._invalidate_cache()

                return jsonify(
                    {
                        "success": True,
                        "message": f"Deleted all documents: {file_count} files and {db_count} database records",
                        "deleted_files": file_count,
                        "deleted_db_records": db_count,
                    }
                )
            except Exception as e:
                self.logger.error(f"Error deleting all documents: {e}")
                return jsonify({"success": False, "message": str(e)}), 500

        @self.app.route("/api/database/reset", methods=["POST"])
        def api_reset_database():
            """Reset the entire database - recreate with fresh schema"""
            try:
                import os
                import shutil
                import time

                from document_database import DocumentDatabase

                # Backup the current database
                db_path = "/app/data/reposense.db"  # Use consolidated database
                backup_path = f"{db_path}.backup.{int(time.time())}"

                if os.path.exists(db_path):
                    shutil.copy2(db_path, backup_path)
                    self.logger.info(f"Created database backup: {backup_path}")

                # Delete the current database file
                if os.path.exists(db_path):
                    os.remove(db_path)
                    self.logger.info(f"Deleted database file: {db_path}")

                # Create a new database with fresh schema
                new_db = DocumentDatabase(db_path)
                self.logger.info(f"Created new database with fresh schema: {db_path}")

                # Also delete all document files
                repositories_dir = "/app/data/output/repositories"
                file_count = 0
                if os.path.exists(repositories_dir):
                    for root, dirs, files in os.walk(repositories_dir):
                        file_count += len([f for f in files if f.endswith(".md")])
                    shutil.rmtree(repositories_dir)
                    self.logger.info(f"Deleted {file_count} document files")

                # Clear document service cache
                self.document_service.force_rescan()

                return jsonify(
                    {
                        "success": True,
                        "message": f"Database reset successfully. Backup created: {os.path.basename(backup_path)}",
                        "backup_file": os.path.basename(backup_path),
                        "deleted_files": file_count,
                    }
                )

            except Exception as e:
                self.logger.error(f"Error resetting database: {e}")
                return jsonify({"success": False, "message": str(e)}), 500

        @self.app.route("/api/system/reset", methods=["POST"])
        def api_reset_system():
            """Enhanced system reset with granular options and safety validation"""
            try:
                import os
                import secrets
                import shutil
                import time

                from document_database import DocumentDatabase
                from models import Config

                # Get reset options from request
                data = request.get_json() or {}
                reset_options = {
                    "database": data.get(
                        "reset_database", True
                    ),  # Default to true for backward compatibility
                    "repositories": data.get("reset_repositories", False),
                    "users": data.get("reset_users", False),
                    "email_config": data.get("reset_email_config", False),
                    "svn_config": data.get("reset_svn_config", False),
                    "ai_config": data.get("reset_ai_config", False),
                    "web_config": data.get("reset_web_config", False),
                    "complete_reset": data.get("complete_reset", False),
                }

                # If complete_reset is true, enable all options
                if reset_options["complete_reset"]:
                    for key in reset_options:
                        if key != "complete_reset":
                            reset_options[key] = True

                # Validation: Ensure we don't create an invalid system state
                validation_errors = []

                # If resetting web config, ensure we have valid defaults
                if reset_options["web_config"]:
                    # Web config reset is safe - we have good defaults
                    pass

                # If resetting AI config, ensure we have a fallback
                if reset_options["ai_config"]:
                    # AI config reset is safe - defaults to localhost:11434
                    pass

                # If resetting users but keeping email config with recipients, warn about orphaned emails
                if reset_options["users"] and not reset_options["email_config"]:
                    # This is safe but might leave orphaned email addresses
                    pass

                if validation_errors:
                    return jsonify(
                        {
                            "success": False,
                            "message": "Validation failed: "
                            + "; ".join(validation_errors),
                        }
                    ), 400

                # Create comprehensive backup
                timestamp = int(time.time())
                backup_info = {
                    "timestamp": timestamp,
                    "reset_options": reset_options,
                    "backups_created": [],
                }

                # Backup database if it exists and will be reset
                db_path = "/app/data/reposense.db"  # Use consolidated database
                if os.path.exists(db_path) and reset_options["database"]:
                    db_backup_path = f"{db_path}.backup.{timestamp}"
                    shutil.copy2(db_path, db_backup_path)
                    backup_info["database_backup"] = os.path.basename(db_backup_path)
                    backup_info["backups_created"].append(
                        f"Database: {os.path.basename(db_backup_path)}"
                    )
                    self.logger.info(f"Created database backup: {db_backup_path}")

                # Always backup config.json (since any config reset affects it)
                config_path = "/app/data/config.json"
                if os.path.exists(config_path):
                    config_backup_path = f"{config_path}.backup.{timestamp}"
                    shutil.copy2(config_path, config_backup_path)
                    backup_info["config_backup"] = os.path.basename(config_backup_path)
                    backup_info["backups_created"].append(
                        f"Configuration: {os.path.basename(config_backup_path)}"
                    )
                    self.logger.info(f"Created config backup: {config_backup_path}")

                # Backup repository files if database is being reset (since they're related)
                repos_path = "/app/data/repositories"
                if os.path.exists(repos_path) and reset_options["database"]:
                    repos_backup_path = f"{repos_path}.backup.{timestamp}"
                    shutil.copytree(repos_path, repos_backup_path)
                    backup_info["repositories_backup"] = os.path.basename(
                        repos_backup_path
                    )
                    backup_info["backups_created"].append(
                        f"Repository files: {os.path.basename(repos_backup_path)}"
                    )
                    self.logger.info(
                        f"Created repositories backup: {repos_backup_path}"
                    )

                # Backup generated documents if database is being reset
                output_path = "/app/data/output"
                if os.path.exists(output_path) and reset_options["database"]:
                    output_backup_path = f"{output_path}.backup.{timestamp}"
                    shutil.copytree(output_path, output_backup_path)
                    backup_info["output_backup"] = os.path.basename(output_backup_path)
                    backup_info["backups_created"].append(
                        f"Generated documents: {os.path.basename(output_backup_path)}"
                    )
                    self.logger.info(f"Created output backup: {output_backup_path}")

                # Backup current log file
                log_path = "/app/data/reposense_ai.log"
                if os.path.exists(log_path):
                    log_backup_path = f"{log_path}.backup.{timestamp}"
                    shutil.copy2(log_path, log_backup_path)
                    backup_info["log_backup"] = os.path.basename(log_backup_path)
                    backup_info["backups_created"].append(
                        f"System log: {os.path.basename(log_backup_path)}"
                    )
                    self.logger.info(f"Created log backup: {log_backup_path}")

                # Load current config for selective reset
                current_config = self.config_manager.load_config()
                reset_summary = []

                # Reset database and documents
                if reset_options["database"]:
                    if os.path.exists(db_path):
                        os.remove(db_path)
                        self.logger.info(f"Deleted database file: {db_path}")

                    # Create new database
                    new_db = DocumentDatabase(db_path)
                    self.logger.info(f"Created new database with fresh schema")

                    # Delete document files
                    repositories_dir = "/app/data/output/repositories"
                    file_count = 0
                    if os.path.exists(repositories_dir):
                        for root, dirs, files in os.walk(repositories_dir):
                            file_count += len([f for f in files if f.endswith(".md")])
                        shutil.rmtree(repositories_dir)
                        self.logger.info(f"Deleted {file_count} document files")

                    # Clear document service cache
                    self.document_service.force_rescan()
                    reset_summary.append(f"Database and {file_count} documents")

                # Reset configuration components
                if any(
                    [
                        reset_options["repositories"],
                        reset_options["users"],
                        reset_options["email_config"],
                        reset_options["svn_config"],
                        reset_options["ai_config"],
                        reset_options["web_config"],
                    ]
                ):
                    # Create fresh config with current schema (not based on old config structure)
                    new_config = (
                        self.config_manager._create_fresh_config_with_current_schema()
                    )
                    self.logger.info("Created fresh config with current system schema")

                    # Migrate/preserve settings that are NOT being reset FROM old config TO new schema
                    if not reset_options["repositories"]:
                        # Migrate repositories from old config to new schema
                        new_config.repositories = (
                            current_config.repositories
                            if hasattr(current_config, "repositories")
                            else []
                        )
                    else:
                        reset_summary.append(
                            f"{len(getattr(current_config, 'repositories', []))} repositories"
                        )

                    if reset_options["users"]:
                        # Users are now in database, not config
                        reset_summary.append(
                            f"{len(getattr(current_config, 'users', []))} users"
                        )

                    if not reset_options["email_config"]:
                        # Preserve email settings
                        new_config.smtp_host = current_config.smtp_host
                        new_config.smtp_port = current_config.smtp_port
                        new_config.smtp_username = current_config.smtp_username
                        new_config.smtp_password = current_config.smtp_password
                        new_config.email_from = current_config.email_from
                        new_config.email_recipients = current_config.email_recipients
                        new_config.send_emails = current_config.send_emails
                    else:
                        reset_summary.append("Email configuration")

                    if not reset_options["svn_config"]:
                        # Migrate SVN settings from old config to new schema
                        new_config.svn_server_url = getattr(
                            current_config, "svn_server_url", ""
                        )
                        new_config.svn_server_username = getattr(
                            current_config, "svn_server_username", None
                        )
                        new_config.svn_server_password = getattr(
                            current_config, "svn_server_password", None
                        )
                        new_config.svn_server_type = getattr(
                            current_config, "svn_server_type", "auto"
                        )

                        # Migrate to new server configuration schema
                        if hasattr(current_config, "server") and current_config.server:
                            # Preserve existing server config
                            new_config.server.name = current_config.server.name
                            new_config.server.description = (
                                current_config.server.description
                            )
                            new_config.server.base_url = current_config.server.base_url
                            new_config.server.default_username = (
                                current_config.server.default_username
                            )
                            new_config.server.default_password = (
                                current_config.server.default_password
                            )
                            self.logger.info("Preserved existing server configuration")
                        else:
                            # Migrate legacy SVN settings to new server schema
                            new_config.server.name = "default"
                            new_config.server.description = ""
                            new_config.server.base_url = getattr(
                                current_config, "svn_server_url", ""
                            )
                            new_config.server.default_username = getattr(
                                current_config, "svn_server_username", None
                            )
                            new_config.server.default_password = getattr(
                                current_config, "svn_server_password", None
                            )
                            self.logger.info(
                                "Migrated legacy SVN settings to new server schema"
                            )
                    else:
                        # Reset SVN config - new schema already has proper defaults
                        reset_summary.append("SVN server configuration")

                    if not reset_options["ai_config"]:
                        # Preserve AI settings
                        new_config.ollama_host = current_config.ollama_host
                        new_config.ollama_model = current_config.ollama_model
                        new_config.ollama_model_documentation = (
                            current_config.ollama_model_documentation
                        )
                        new_config.ollama_model_code_review = (
                            current_config.ollama_model_code_review
                        )
                        new_config.ollama_model_risk_assessment = (
                            current_config.ollama_model_risk_assessment
                        )
                        new_config.ollama_timeout_base = (
                            current_config.ollama_timeout_base
                        )
                        new_config.ollama_timeout_connection = (
                            current_config.ollama_timeout_connection
                        )
                        new_config.ollama_timeout_embeddings = (
                            current_config.ollama_timeout_embeddings
                        )
                        new_config.use_enhanced_prompts = (
                            current_config.use_enhanced_prompts
                        )
                        new_config.enhanced_prompts_fallback = (
                            current_config.enhanced_prompts_fallback
                        )
                    else:
                        reset_summary.append("AI model configuration")

                    if not reset_options["web_config"]:
                        # Preserve web settings (but generate new secret key for security)
                        new_config.web_enabled = current_config.web_enabled
                        new_config.web_port = current_config.web_port
                        new_config.web_host = current_config.web_host
                        new_config.web_log_entries = current_config.web_log_entries
                        new_config.log_cleanup_max_size_mb = (
                            current_config.log_cleanup_max_size_mb
                        )
                        new_config.log_cleanup_lines_to_keep = (
                            current_config.log_cleanup_lines_to_keep
                        )
                        new_config.log_rotation_max_size_mb = (
                            current_config.log_rotation_max_size_mb
                        )
                        new_config.log_rotation_backup_count = (
                            current_config.log_rotation_backup_count
                        )
                        # Always preserve the secret key unless explicitly resetting web config
                        new_config.web_secret_key = current_config.web_secret_key
                    else:
                        # Generate new secret key for security
                        new_config.web_secret_key = secrets.token_hex(32)
                        reset_summary.append("Web interface configuration")

                    # Always preserve some critical settings to avoid breaking the system
                    new_config.output_dir = current_config.output_dir
                    new_config.generate_docs = current_config.generate_docs
                    new_config.check_interval = current_config.check_interval
                    new_config.skip_initial_scan = current_config.skip_initial_scan
                    new_config.cleanup_orphaned_documents = (
                        current_config.cleanup_orphaned_documents
                    )

                    # Save the new configuration
                    self.config_manager.save_config(new_config)
                    self.logger.info(
                        f"Configuration reset with options: {reset_options}"
                    )

                return jsonify(
                    {
                        "success": True,
                        "message": f"System reset completed. Reset: {', '.join(reset_summary)}",
                        "backup_info": backup_info,
                        "reset_summary": reset_summary,
                        "reset_options": reset_options,
                    }
                )

            except Exception as e:
                self.logger.error(f"Error resetting system: {e}")
                return jsonify({"success": False, "message": str(e)}), 500
            except Exception as e:
                self.logger.error(f"Error resetting database: {e}")
                return jsonify({"success": False, "message": str(e)}), 500

        # Historical Scanning Routes
        @self.app.route("/repositories/<repo_id>/historical-scan")
        def historical_scan_page(repo_id):
            """Display historical scanning configuration for a repository"""
            repo = self.monitor_service.repo_db.get_repository_by_id(repo_id)

            if not repo:
                flash("Repository not found", "error")
                return redirect(url_for("repositories_page"))

            # Get scan progress if any
            scan_progress = self.historical_scanner.get_scan_progress(repo_id)

            return render_template(
                "historical_scan.html",
                repository=repo,
                scan_progress=scan_progress,
                scan_statuses=HistoricalScanStatus,
            )

        @self.app.route(
            "/repositories/<repo_id>/historical-scan/configure", methods=["POST"]
        )
        def configure_historical_scan(repo_id):
            """Configure historical scanning for a repository"""
            try:
                repo = self.monitor_service.repo_db.get_repository_by_id(repo_id)

                if not repo:
                    return jsonify(
                        {"success": False, "message": "Repository not found"}
                    )

                # Parse form data
                scan_config = HistoricalScanConfig()
                scan_config.enabled = request.form.get("enabled") == "on"
                scan_config.scan_by_revision = (
                    request.form.get("scan_by_revision") == "on"
                )
                scan_config.scan_by_date = request.form.get("scan_by_date") == "on"

                # Revision range
                if scan_config.scan_by_revision:
                    start_rev = request.form.get("start_revision", "").strip()
                    end_rev = request.form.get("end_revision", "").strip()
                    scan_config.start_revision = int(start_rev) if start_rev else None
                    scan_config.end_revision = int(end_rev) if end_rev else None

                # Date range
                if scan_config.scan_by_date:
                    from datetime import datetime

                    start_date = request.form.get("start_date", "").strip()
                    end_date = request.form.get("end_date", "").strip()
                    if start_date:
                        scan_config.start_date = datetime.fromisoformat(start_date)
                    if end_date:
                        scan_config.end_date = datetime.fromisoformat(end_date)

                # Other settings
                scan_config.batch_size = int(request.form.get("batch_size", 10))
                scan_config.include_merge_commits = (
                    request.form.get("include_merge_commits") == "on"
                )
                scan_config.skip_large_commits = (
                    request.form.get("skip_large_commits") == "on"
                )
                scan_config.max_files_per_commit = int(
                    request.form.get("max_files_per_commit", 100)
                )
                scan_config.force_rescan = request.form.get("force_rescan") == "on"

                # Analysis preferences
                scan_config.generate_documentation = (
                    request.form.get("generate_documentation") == "on"
                )
                scan_config.analyze_code_review = (
                    request.form.get("analyze_code_review") == "on"
                )
                scan_config.analyze_documentation_impact = (
                    request.form.get("analyze_documentation_impact") == "on"
                )

                # Update repository configuration
                repo.historical_scan = scan_config
                success = self.monitor_service.repo_db.update_repository(repo)
                if not success:
                    return jsonify(
                        {
                            "success": False,
                            "message": "Failed to save repository configuration",
                        }
                    )

                return jsonify(
                    {"success": True, "message": "Historical scan configuration saved"}
                )

            except Exception as e:
                return jsonify({"success": False, "message": str(e)})

        @self.app.route(
            "/repositories/<repo_id>/historical-scan/start", methods=["POST"]
        )
        def start_historical_scan(repo_id):
            """Start historical scanning for a repository"""
            try:
                repo = self.monitor_service.repo_db.get_repository_by_id(repo_id)

                if not repo:
                    return jsonify(
                        {"success": False, "message": "Repository not found"}
                    )

                # Always update configuration with form data if provided (auto-save on start)
                if request.form:
                    try:
                        # Parse form data and save configuration first
                        if not repo.historical_scan:
                            scan_config = HistoricalScanConfig()
                        else:
                            scan_config = repo.historical_scan

                        scan_config.enabled = request.form.get("enabled") == "on"
                        scan_config.scan_by_revision = (
                            request.form.get("scan_by_revision") == "on"
                        )
                        scan_config.scan_by_date = (
                            request.form.get("scan_by_date") == "on"
                        )

                        # Revision range
                        if scan_config.scan_by_revision:
                            start_rev = request.form.get("start_revision", "").strip()
                            end_rev = request.form.get("end_revision", "").strip()
                            scan_config.start_revision = (
                                int(start_rev) if start_rev else None
                            )
                            scan_config.end_revision = int(end_rev) if end_rev else None

                            # Date range
                            if scan_config.scan_by_date:
                                from datetime import datetime

                                start_date = request.form.get("start_date", "").strip()
                                end_date = request.form.get("end_date", "").strip()
                                if start_date:
                                    scan_config.start_date = datetime.fromisoformat(
                                        start_date
                                    )
                                if end_date:
                                    scan_config.end_date = datetime.fromisoformat(
                                        end_date
                                    )

                            # Other settings
                            scan_config.batch_size = int(
                                request.form.get("batch_size", 10)
                            )
                            scan_config.include_merge_commits = (
                                request.form.get("include_merge_commits") == "on"
                            )
                            scan_config.skip_large_commits = (
                                request.form.get("skip_large_commits") == "on"
                            )
                            scan_config.max_files_per_commit = int(
                                request.form.get("max_files_per_commit", 100)
                            )
                            scan_config.force_rescan = (
                                request.form.get("force_rescan") == "on"
                            )

                            # Analysis preferences
                            scan_config.generate_documentation = (
                                request.form.get("generate_documentation") == "on"
                            )
                            scan_config.analyze_code_review = (
                                request.form.get("analyze_code_review") == "on"
                            )
                            scan_config.analyze_documentation_impact = (
                                request.form.get("analyze_documentation_impact") == "on"
                            )

                            # Update repository configuration
                            repo.historical_scan = scan_config
                            self.monitor_service.save_config()

                            # Verify configuration is now enabled
                            if not scan_config.enabled:
                                return jsonify(
                                    {
                                        "success": False,
                                        "message": "Historical scanning must be enabled to start scan",
                                    }
                                )

                    except Exception as e:
                        return jsonify(
                            {
                                "success": False,
                                "message": f"Error auto-configuring scan: {str(e)}",
                            }
                        )

                # Check if historical scanning is configured and enabled
                if not repo.historical_scan or not repo.historical_scan.enabled:
                    # Auto-configure with sensible defaults for newly imported repositories
                    try:
                        self.logger.info(
                            f"Auto-configuring historical scan for {repo.name}"
                        )

                        # Get backend to determine revision range
                        backend = self.monitor_service.backend_manager.get_backend_for_repository(
                            repo, None
                        )
                        if not backend:
                            return jsonify(
                                {
                                    "success": False,
                                    "message": "No backend available for repository",
                                }
                            )

                        # Get the latest revision
                        latest_revision = backend.get_latest_revision(repo)
                        if not latest_revision:
                            return jsonify(
                                {
                                    "success": False,
                                    "message": "Could not determine latest revision",
                                }
                            )

                        # Create default scan configuration for last 10 revisions
                        latest_rev_int = int(latest_revision)
                        start_revision = max(
                            1, latest_rev_int - 9
                        )  # Last 10 revisions, but not below 1

                        scan_config = HistoricalScanConfig(
                            enabled=True,
                            scan_by_revision=True,
                            start_revision=start_revision,  # Start from 10 revisions ago
                            end_revision=latest_rev_int,  # Scan up to latest
                            batch_size=10,
                            include_merge_commits=True,
                            skip_large_commits=False,
                            max_files_per_commit=100,
                        )

                        # Update repository configuration
                        repo.historical_scan = scan_config
                        success = self.monitor_service.repo_db.update_repository(repo)
                        if not success:
                            return jsonify(
                                {
                                    "success": False,
                                    "message": "Failed to save repository configuration",
                                }
                            )

                        self.logger.info(
                            f"Auto-configured historical scan for {repo.name}: revisions {start_revision}-{latest_rev_int} (last 10 revisions)"
                        )

                    except Exception as e:
                        self.logger.error(
                            f"Error auto-configuring historical scan for {repo.name}: {e}"
                        )
                        return jsonify(
                            {
                                "success": False,
                                "message": f"Could not auto-configure historical scanning: {str(e)}",
                            }
                        )

                # Final check
                if not repo.historical_scan or not repo.historical_scan.enabled:
                    return jsonify(
                        {
                            "success": False,
                            "message": "Historical scanning could not be configured. Please configure manually first.",
                        }
                    )

                # Queue the scan
                success = self.historical_scanner.queue_scan(repo, repo.historical_scan)

                if success:
                    return jsonify(
                        {"success": True, "message": "Historical scan started"}
                    )
                else:
                    # Check if there's a specific error message from the scanner
                    scan_progress = self.historical_scanner.get_scan_progress(repo.id)
                    if scan_progress and scan_progress.error_message:
                        return jsonify(
                            {"success": False, "message": scan_progress.error_message}
                        )
                    else:
                        return jsonify(
                            {
                                "success": False,
                                "message": "Failed to start scan - please check your configuration",
                            }
                        )

            except Exception as e:
                return jsonify({"success": False, "message": str(e)})

        @self.app.route(
            "/repositories/<repo_id>/historical-scan/cancel", methods=["POST"]
        )
        def cancel_historical_scan(repo_id):
            """Cancel historical scanning for a repository"""
            try:
                success = self.historical_scanner.cancel_scan(repo_id)

                if success:
                    return jsonify(
                        {"success": True, "message": "Historical scan cancelled"}
                    )
                else:
                    return jsonify(
                        {"success": False, "message": "No active scan to cancel"}
                    )

            except Exception as e:
                return jsonify({"success": False, "message": str(e)})

        @self.app.route(
            "/repositories/<repo_id>/historical-scan/reset", methods=["POST"]
        )
        def reset_historical_scan(repo_id):
            """Reset historical scan status for a repository"""
            try:
                # Find the repository
                repo = None
                for r in self.monitor_service.config.repositories:
                    if r.id == repo_id:
                        repo = r
                        break

                if not repo:
                    return jsonify(
                        {"success": False, "message": "Repository not found"}
                    )

                # Reset historical scan status
                if repo.historical_scan:
                    repo.historical_scan.last_scanned_revision = None
                    repo.historical_scan.scan_status = HistoricalScanStatus.NOT_STARTED
                    repo.historical_scan.scan_started_at = None
                    repo.historical_scan.scan_completed_at = None
                    repo.historical_scan.processed_revisions = 0
                    repo.historical_scan.failed_revisions = 0
                    repo.historical_scan.error_message = None

                    # Save configuration
                    self.monitor_service.save_config()

                    # Clear any completed scan progress from memory
                    if repo_id in self.historical_scanner.completed_scans:
                        del self.historical_scanner.completed_scans[repo_id]

                    self.logger.info(
                        f"Reset historical scan status for repository {repo.name}"
                    )
                    return jsonify(
                        {
                            "success": True,
                            "message": "Historical scan status reset successfully",
                        }
                    )
                else:
                    return jsonify(
                        {
                            "success": False,
                            "message": "No historical scan configuration found",
                        }
                    )

            except Exception as e:
                self.logger.error(f"Error resetting historical scan for {repo_id}: {e}")
                return jsonify({"success": False, "message": str(e)})

        @self.app.route("/api/historical-scan/progress")
        def api_historical_scan_progress():
            """Get progress for all historical scans"""
            try:
                all_progress = self.historical_scanner.get_all_scan_progress()
                statistics = self.historical_scanner.get_statistics()

                # Convert progress objects to dictionaries
                progress_data = {}
                for repo_id, progress in all_progress.items():
                    progress_data[repo_id] = {
                        "repository_id": progress.repository_id,
                        "total_revisions": progress.total_revisions,
                        "processed_revisions": progress.processed_revisions,
                        "failed_revisions": progress.failed_revisions,
                        "current_revision": progress.current_revision,
                        "started_at": progress.started_at.isoformat()
                        if progress.started_at
                        else None,
                        "estimated_completion": progress.estimated_completion.isoformat()
                        if progress.estimated_completion
                        else None,
                        "error_message": progress.error_message,
                        "status": progress.status.value,
                    }

                return jsonify({"progress": progress_data, "statistics": statistics})

            except Exception as e:
                return jsonify({"error": str(e)}), 500

        @self.app.route("/api/documents/processing-stats")
        def api_processing_stats():
            """Get document processing statistics"""
            return jsonify(self.document_service.get_processing_stats())

        @self.app.route("/api/documents/cache-stats")
        def api_cache_stats():
            """Get cache performance statistics"""
            return jsonify(self.document_service.get_cache_stats())

        @self.app.route("/api/documents/migration-status")
        def api_migration_status():
            """Get database migration status"""
            return jsonify(self.document_service.get_migration_status())

        @self.app.route("/api/documents/<path:doc_id>/diff")
        def api_get_document_diff(doc_id):
            """Get document diff content generated on-demand"""
            try:
                self.logger.info(f"API diff request for document: {doc_id}")
                from diff_service import DiffService

                # Try to get document with original ID
                document = self.document_service.get_document_record_by_id(doc_id)

                # If not found, try alternative ID formats (same logic as other endpoints)
                if not document:
                    self.logger.info(
                        f"Document not found with original ID: {doc_id}, trying alternative formats"
                    )

                    # Try removing "_revision_" if it exists
                    if "_revision_" in doc_id:
                        alternative_id = doc_id.replace("_revision_", "_")
                        self.logger.info(
                            f"Trying alternative document ID format: {alternative_id}"
                        )
                        document = self.document_service.get_document_record_by_id(
                            alternative_id
                        )
                        if document:
                            doc_id = alternative_id  # Update doc_id for subsequent operations
                            self.logger.info(
                                f"Found document with alternative ID: {alternative_id}"
                            )

                if not document:
                    self.logger.warning(
                        f"Document not found for diff generation: {doc_id}"
                    )
                    return jsonify(
                        {
                            "error": f'Document not found. Unable to generate diff for document ID "{doc_id}".',
                            "error_code": "DOCUMENT_NOT_FOUND",
                        }
                    ), 404

                # Get format parameter (unified or side-by-side)
                format_type = request.args.get("format", "unified")
                if format_type not in ["unified", "side-by-side"]:
                    return jsonify(
                        {"error": 'Invalid format. Use "unified" or "side-by-side"'}
                    ), 400

                self.logger.info(
                    f"Generating {format_type} diff for document: {doc_id}"
                )

                # Generate diff on-demand using DiffService
                diff_service = DiffService(self.monitor_service.config_manager)

                if not diff_service.can_generate_diff(document):
                    self.logger.warning(
                        f"Cannot generate diff for document {doc_id} - missing repository metadata"
                    )
                    return jsonify(
                        {
                            "error": "Cannot generate diff for this document. The document may be missing repository metadata or the repository may no longer be accessible.",
                            "error_code": "DIFF_GENERATION_FAILED",
                        }
                    ), 404

                diff_content = diff_service.get_diff_for_document(document, format_type)
                if not diff_content:
                    self.logger.error(
                        f"Failed to generate diff content for document: {doc_id}"
                    )
                    return jsonify({"error": "Failed to generate diff content"}), 500

                self.logger.info(f"Successfully generated diff for document: {doc_id}")
                return jsonify({"diff": diff_content, "format": format_type})

            except Exception as e:
                self.logger.error(
                    f"Error in diff API for document {doc_id}: {str(e)}", exc_info=True
                )
                return jsonify(
                    {
                        "error": f"Internal server error: {str(e)}",
                        "error_code": "INTERNAL_ERROR",
                    }
                ), 500

        @self.app.route("/api/documents/<path:doc_id>/ai-suggestions")
        def api_get_ai_suggestions(doc_id):
            """Get AI documentation suggestions asynchronously"""
            try:
                document = self.document_service.get_document_by_id(doc_id)
                if not document:
                    self.logger.warning(
                        f"Document not found for AI suggestions: {doc_id}"
                    )
                    return jsonify(
                        {
                            "error": f'Document not found. Unable to generate AI suggestions for document ID "{doc_id}".',
                            "error_code": "DOCUMENT_NOT_FOUND",
                        }
                    ), 404

                # Generate AI suggestions (this may take time)
                ai_suggestions = self.document_service.get_ai_documentation_suggestions(
                    doc_id
                )

                return jsonify({"suggestions": ai_suggestions, "success": True})
            except Exception as e:
                self.logger.error(f"Error getting AI suggestions for {doc_id}: {e}")
                return jsonify(
                    {"error": "Failed to generate AI suggestions", "success": False}
                ), 500

        @self.app.route("/api/documents/<path:doc_id>/download/pdf", methods=["POST"])
        def api_download_document_pdf(doc_id):
            """Generate and download document as PDF"""
            try:
                from pdf_generator import PDFGenerator

                document = self.document_service.get_document_by_id(doc_id)
                if not document:
                    self.logger.warning(
                        f"Document not found for PDF generation: {doc_id}"
                    )
                    return jsonify(
                        {
                            "error": f'Document not found. Unable to generate PDF for document ID "{doc_id}".',
                            "error_code": "DOCUMENT_NOT_FOUND",
                        }
                    ), 404

                # Get request data
                data = request.get_json()
                if not data:
                    return jsonify({"error": "No data provided"}), 400

                content = data.get("content", "")
                diff_content = data.get("diff_content", "")
                document_info = data.get("document_info", {})

                # Generate PDF
                pdf_generator = PDFGenerator()
                pdf_bytes = pdf_generator.generate_document_pdf(
                    content=content,
                    diff_content=diff_content,
                    document_info=document_info,
                    _document=document,
                )

                if not pdf_bytes:
                    return jsonify({"error": "Failed to generate PDF"}), 500

                # Return PDF as response
                response = make_response(pdf_bytes)
                response.headers["Content-Type"] = "application/pdf"
                response.headers["Content-Disposition"] = (
                    f'attachment; filename="{document.display_name.replace(" ", "_")}.pdf"'
                )
                # No-store headers so downloads don’t get cached stale by proxies
                response.headers["Cache-Control"] = (
                    "no-store, no-cache, must-revalidate, max-age=0, private"
                )
                response.headers["Pragma"] = "no-cache"
                response.headers["Expires"] = "0"
                return response

            except ImportError:
                return jsonify(
                    {"error": "PDF generation not available - missing dependencies"}
                ), 500
            except Exception as e:
                self.logger.error(f"PDF generation failed: {e}")
                return jsonify({"error": "PDF generation failed"}), 500

        @self.app.route(
            "/api/documents/<path:doc_id>/feedback/code-review", methods=["POST"]
        )
        def api_update_code_review_feedback(doc_id):
            """Update code review feedback for a document"""
            try:
                data = request.get_json()
                if not data:
                    return jsonify({"error": "No data provided"}), 400

                status = data.get("status")
                comments = data.get("comments")
                reviewer = data.get("reviewer")

                if not status:
                    return jsonify({"error": "Status is required"}), 400

                if status not in [
                    "approved",
                    "rejected",
                    "needs_changes",
                    "in_progress",
                ]:
                    return jsonify(
                        {
                            "error": "Invalid status. Use: approved, rejected, needs_changes, in_progress"
                        }
                    ), 400

                success = self.document_service.update_code_review_feedback(
                    doc_id, status, comments, reviewer
                )
                if success:
                    return jsonify(
                        {"success": True, "message": "Code review feedback updated"}
                    )
                else:
                    return jsonify(
                        {"error": "Failed to update code review feedback"}
                    ), 500

            except Exception as e:
                self.logger.error(f"Error updating code review feedback: {e}")
                return jsonify({"error": "Internal server error"}), 500

        @self.app.route(
            "/api/documents/<path:doc_id>/feedback/documentation", methods=["POST"]
        )
        def api_update_documentation_feedback(doc_id):
            """Update documentation quality feedback for a document"""
            try:
                data = request.get_json()
                if not data:
                    return jsonify({"error": "No data provided"}), 400

                rating = data.get("rating")
                comments = data.get("comments")
                updated_by = data.get("updated_by")

                if rating is not None:
                    try:
                        rating = int(rating)
                        if rating < 1 or rating > 5:
                            return jsonify(
                                {"error": "Rating must be between 1 and 5"}
                            ), 400
                    except (ValueError, TypeError):
                        return jsonify(
                            {"error": "Rating must be a number between 1 and 5"}
                        ), 400

                success = self.document_service.update_documentation_feedback(
                    doc_id, rating, comments, updated_by
                )
                if success:
                    return jsonify(
                        {"success": True, "message": "Documentation feedback updated"}
                    )
                else:
                    return jsonify(
                        {"error": "Failed to update documentation feedback"}
                    ), 500

            except Exception as e:
                self.logger.error(f"Error updating documentation feedback: {e}")
                return jsonify({"error": "Internal server error"}), 500

        @self.app.route(
            "/api/documents/<path:doc_id>/feedback/risk-assessment", methods=["POST"]
        )
        def api_update_risk_assessment_feedback(doc_id):
            """Update risk assessment override for a document"""
            try:
                data = request.get_json()
                if not data:
                    return jsonify({"error": "No data provided"}), 400

                risk_override = data.get("risk_override")
                comments = data.get("comments")
                updated_by = data.get("updated_by")

                if not risk_override:
                    return jsonify({"error": "Risk override is required"}), 400

                if risk_override not in ["CRITICAL", "HIGH", "MEDIUM", "LOW"]:
                    return jsonify(
                        {
                            "error": "Invalid risk level. Use: CRITICAL, HIGH, MEDIUM, LOW"
                        }
                    ), 400

                success = self.document_service.update_risk_assessment_override(
                    doc_id, risk_override, comments, updated_by
                )
                if success:
                    return jsonify(
                        {"success": True, "message": "Risk assessment override updated"}
                    )
                else:
                    return jsonify(
                        {"error": "Failed to update risk assessment override"}
                    ), 500

            except Exception as e:
                self.logger.error(f"Error updating risk assessment override: {e}")
                return jsonify({"error": "Internal server error"}), 500

        @self.app.route(
            "/api/documents/<path:doc_id>/feedback/documentation-input",
            methods=["POST"],
        )
        def api_update_documentation_input(doc_id):
            """Update user documentation input/augmentation for a document"""
            try:
                data = request.get_json()
                if not data:
                    return jsonify({"error": "No data provided"}), 400

                documentation_input = data.get("documentation_input")
                suggestions = data.get("suggestions")
                input_by = data.get("input_by")

                if not documentation_input and not suggestions:
                    return jsonify(
                        {
                            "error": "Either documentation input or suggestions is required"
                        }
                    ), 400

                success = self.document_service.update_documentation_input(
                    doc_id, documentation_input, suggestions, input_by
                )
                if success:
                    return jsonify(
                        {"success": True, "message": "Documentation input updated"}
                    )
                else:
                    return jsonify(
                        {"error": "Failed to update documentation input"}
                    ), 500

            except Exception as e:
                self.logger.error(f"Error updating documentation input: {e}")
                return jsonify({"error": "Internal server error"}), 500

        @self.app.route("/api/repositories/<repo_id>/browse")
        def browse_repository(repo_id):
            """Browse repository files for document selection"""
            try:
                path = request.args.get("path", "/")
                filter_docs = request.args.get("filter_docs", "true").lower() == "true"

                # Get repository configuration
                repo = self.monitor_service.repo_db.get_repository_by_id(repo_id)
                if not repo:
                    return jsonify(
                        {"success": False, "error": "Repository not found"}
                    ), 404

                # Get the appropriate backend for this repository
                backend = self.backend_manager.get_backend_for_repository(
                    repo, self.monitor_service.config
                )
                if not backend:
                    return jsonify(
                        {"success": False, "error": "Unsupported repository type"}
                    ), 400

                # Browse the repository at the specified path
                files = backend.browse_files(
                    repo.url, repo.username, repo.password, path
                )

                # Filter and format the results for the frontend
                formatted_files = []
                for file_info in files:
                    # Apply filtering based on filter_docs parameter
                    if filter_docs:
                        # Only include files that might be documentation or directories
                        if file_info[
                            "type"
                        ] == "directory" or self._is_potential_documentation_file(
                            file_info["name"]
                        ):
                            formatted_files.append(
                                {
                                    "name": file_info["name"],
                                    "type": file_info["type"],
                                    "size": file_info.get("size"),
                                    "modified": file_info.get("modified"),
                                }
                            )
                    else:
                        # Include all files and directories
                        formatted_files.append(
                            {
                                "name": file_info["name"],
                                "type": file_info["type"],
                                "size": file_info.get("size"),
                                "modified": file_info.get("modified"),
                            }
                        )

                return jsonify(
                    {"success": True, "files": formatted_files, "path": path}
                )

            except Exception as e:
                self.logger.error(f"Error browsing repository {repo_id}: {e}")
                return jsonify(
                    {
                        "success": False,
                        "error": f"Failed to browse repository: {str(e)}",
                    }
                ), 500

    def _is_potential_documentation_file(self, filename: str) -> bool:
        """Check if a file might be documentation based on its name and extension"""
        filename_lower = filename.lower()

        # Common documentation file extensions (including Microsoft Office formats)
        doc_extensions = [
            ".md",
            ".txt",
            ".rst",
            ".html",
            ".htm",
            ".pdf",
            ".doc",
            ".docx",  # Microsoft Word
            ".rtf",  # Rich Text Format
            ".odt",  # OpenDocument Text
            ".pages",  # Apple Pages
        ]
        if any(filename_lower.endswith(ext) for ext in doc_extensions):
            return True

        # Common documentation file names (without extension)
        doc_names = [
            "readme",
            "changelog",
            "changes",
            "history",
            "news",
            "authors",
            "contributors",
            "license",
            "copying",
            "install",
            "installation",
            "setup",
            "usage",
            "manual",
            "guide",
            "tutorial",
            "howto",
            "faq",
            "api",
            "reference",
            "documentation",
            "docs",
            "features",
            "roadmap",
            "todo",
            "notes",
            "release",
        ]

        filename_base = filename_lower.split(".")[0]
        if filename_base in doc_names:
            return True

        # Files in common documentation directories
        if any(
            keyword in filename_lower
            for keyword in ["doc", "guide", "manual", "help", "wiki"]
        ):
            return True

        return False

    def run(self):
        """Run the web interface"""
        import os

        debug_mode = (
            os.getenv("FLASK_DEBUG", "0") == "1"
            or os.getenv("SVN_MONITOR_ENV") == "development"
        )

        # Add global error handler for API routes to ensure JSON responses
        @self.app.errorhandler(500)
        def handle_internal_error(error):
            if request.path.startswith("/api/"):
                return jsonify(
                    {
                        "success": False,
                        "message": "Internal server error",
                        "error": str(error),
                    }
                ), 500
            return error

        @self.app.errorhandler(404)
        def handle_not_found(error):
            if request.path.startswith("/api/"):
                return jsonify(
                    {
                        "success": False,
                        "message": "API endpoint not found",
                        "error": str(error),
                    }
                ), 404
            return error

        self.logger.info(
            f"Starting web interface on {self.monitor_service.config.web_host}:{self.monitor_service.config.web_port}"
        )
        if debug_mode:
            self.logger.info(
                "Debug mode enabled - templates and static files will auto-reload"
            )

        self.app.run(
            host=self.monitor_service.config.web_host,
            port=self.monitor_service.config.web_port,
            debug=debug_mode,
            use_reloader=debug_mode,
            use_debugger=debug_mode,
        )

    def _create_bulk_scan_config(self, form_data) -> HistoricalScanConfig:
        """Create a HistoricalScanConfig from bulk scan form data"""
        from datetime import datetime

        # Get scan method
        scan_method = form_data.get("scan_scan_method", "revision")

        # Create base configuration
        config = HistoricalScanConfig(
            enabled=True,
            batch_size=int(form_data.get("scan_batch_size", 10)),
            max_files_per_commit=int(form_data.get("scan_max_files_per_commit", 100)),
            include_merge_commits=form_data.get("scan_include_merge_commits") == "on",
            skip_large_commits=form_data.get("scan_skip_large_commits") == "on",
            force_rescan=form_data.get("scan_force_rescan") == "on",
        )

        if scan_method == "revision":
            # Revision-based scanning
            config.scan_by_revision = True
            config.scan_by_date = False

            # Parse revision range
            start_revision = form_data.get("scan_start_revision")
            end_revision = form_data.get("scan_end_revision")

            if start_revision and start_revision.strip():
                config.start_revision = int(start_revision)
            else:
                config.start_revision = None  # Will default to 1 in backend

            if end_revision and end_revision.strip():
                config.end_revision = int(end_revision)
            else:
                config.end_revision = None  # Will default to latest in backend

        elif scan_method == "date":
            # Date-based scanning
            config.scan_by_revision = False
            config.scan_by_date = True

            # Parse date range
            start_date_str = form_data.get("scan_start_date")
            end_date_str = form_data.get("scan_end_date")

            if start_date_str:
                config.start_date = datetime.strptime(start_date_str, "%Y-%m-%d")
            if end_date_str:
                config.end_date = datetime.strptime(end_date_str, "%Y-%m-%d")

        return config

    def _apply_revision_count_to_repo(
        self, config: HistoricalScanConfig, repo, revision_count: int
    ) -> HistoricalScanConfig:
        """Apply revision count logic to a specific repository's scan configuration using actual available revisions"""
        try:
            # Get the repository backend
            backend_type = (
                "svn"
                if repo.url.startswith(("http://", "https://"))
                and "svn" in repo.url.lower()
                else "git"
            )
            backend = self.backend_manager.get_backend(
                backend_type, self.monitor_service.config
            )

            if backend:
                # Get the actual available revisions (limited to what we need)
                available_revisions = backend.get_all_available_revisions(
                    repo, limit=revision_count
                )

                if available_revisions:
                    # available_revisions is in reverse chronological order (newest first)
                    # Take the last N revisions (which are the most recent)
                    revisions_to_scan = available_revisions[:revision_count]

                    if revisions_to_scan:
                        # Get the oldest and newest revisions from our selection
                        # Since available_revisions is newest first, we need to reverse for chronological order
                        revisions_to_scan_chrono = sorted(revisions_to_scan, key=int)
                        start_revision = int(revisions_to_scan_chrono[0])
                        end_revision = int(revisions_to_scan_chrono[-1])

                        # Create a new config with the calculated range
                        new_config = HistoricalScanConfig(
                            enabled=config.enabled,
                            scan_by_revision=config.scan_by_revision,
                            scan_by_date=config.scan_by_date,
                            start_revision=start_revision,
                            end_revision=end_revision,
                            start_date=config.start_date,
                            end_date=config.end_date,
                            batch_size=config.batch_size,
                            include_merge_commits=config.include_merge_commits,
                            skip_large_commits=config.skip_large_commits,
                            max_files_per_commit=config.max_files_per_commit,
                            force_rescan=config.force_rescan,
                        )

                        self.logger.info(
                            f"Applied revision count {revision_count} to {repo.name}: scanning {len(revisions_to_scan)} actual revisions from {start_revision} to {end_revision}"
                        )
                        return new_config
                    else:
                        self.logger.warning(
                            f"No revisions found for {repo.name} when applying count {revision_count}"
                        )
                else:
                    self.logger.warning(
                        f"Could not get available revisions for {repo.name}, using original config"
                    )
            else:
                self.logger.warning(
                    f"Could not get backend for {repo.name}, using original config"
                )

        except Exception as e:
            self.logger.error(f"Error applying revision count to {repo.name}: {e}")

        # Return original config if anything goes wrong
        return config
