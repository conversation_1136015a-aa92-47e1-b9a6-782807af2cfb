#!/usr/bin/env python3
"""
Script to update risk_aggressiveness_used field for existing documents
"""

import sys
import os
sys.path.append('/app')

from document_database import DocumentDatabase
from config_manager import Config<PERSON>anager

def update_risk_aggressiveness():
    """Update risk_aggressiveness_used for existing documents that don't have it set"""
    
    print("🔧 Updating risk aggressiveness for existing documents...")
    
    # Initialize services
    config_manager = ConfigManager()
    doc_db = DocumentDatabase("/app/data/reposense.db")
    
    try:
        # Load current configuration
        config = config_manager.load_config()
        print(f"📋 Loaded configuration with {len(config.repositories)} repositories")
        
        # Get all documents (using large limit to get all)
        documents = doc_db.get_documents(limit=10000, offset=0)
        print(f"📄 Found {len(documents)} documents in database")
        
        updated_count = 0
        
        for doc in documents:
            # Skip if already has risk aggressiveness set
            if doc.risk_aggressiveness_used:
                continue
                
            # Find the repository configuration
            repo_config = None
            for repo in config.repositories:
                if repo.name == doc.repository_name:
                    repo_config = repo
                    break
            
            if not repo_config:
                print(f"⚠️  No repository config found for: {doc.repository_name}")
                continue
                
            # Update the document with the repository's risk aggressiveness
            doc.risk_aggressiveness_used = repo_config.risk_aggressiveness
            
            # Save the updated document
            if doc_db.upsert_document(doc):
                updated_count += 1
                print(f"✅ Updated {doc.repository_name} rev {doc.revision}: {repo_config.risk_aggressiveness}")
            else:
                print(f"❌ Failed to update {doc.repository_name} rev {doc.revision}")
        
        print(f"\n🎉 Successfully updated {updated_count} documents")
        
        # Show repository configurations
        print(f"\n📊 Current repository risk aggressiveness settings:")
        for repo in config.repositories:
            print(f"   {repo.name}: {repo.risk_aggressiveness}")
            
    except Exception as e:
        print(f"❌ Error updating risk aggressiveness: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    update_risk_aggressiveness()
